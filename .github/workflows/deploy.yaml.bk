#name: CI
## configure manual trigger
#on:
#  workflow_dispatch:
#    inputs:
#      branch:
#        description: 'The branch to build'
#        required: true
#  push:
#    branches:
#      - develop
#  pull_request:
#    branches:
#      - develop
#jobs:
#  deploy:
#    runs-on: ubuntu-latest
#    steps:
#      - name: Checkout
#        uses: actions/checkout@main
#
#      - name: Set Node.js 16.x
#        uses: actions/setup-node@v3
#        with:
#          node-version: 18.x
#
#      - name: Install Dependencies
#        run: yarn install
#
#      - name: Build
#        env:
#          ENVIRONMENT: develop
#          NUXT_API_BASE_URL: "https://kqjb8yzdja.execute-api.ap-southeast-1.amazonaws.com/api/v1/"
#          NUXT_FIREBASE_API_KEY: ${{ secrets.FIREBASE_API_KEY }}
#          NUXT_FIREBASE_AUTH_DOMAIN: ${{ secrets.FIREBASE_AUTH_DOMAIN }}
#          NUXT_FIREBASE_DATABASE_URL: ${{ secrets.FIREBASE_DATABASE_URL }}
#          NUXT_FIREBASE_PROJECT_ID: ${{ secrets.FIREBASE_PROJECT_ID }}
#          NUXT_FIREBASE_STORAGE_BUCKET: ${{ secrets.FIREBASE_STORAGE_BUCKET }}
#          NUXT_FIREBASE_MESSAGING_SENDER_ID: ${{ secrets.FIREBASE_MESSAGING_SENDER_ID }}
#          NUXT_FIREBASE_APP_ID: ${{ secrets.FIREBASE_APP_ID }}
#        run: npm run generate
#
#      - name: Deploy
#        env:
#          S3_UPLOAD_BUCKET: ${{ secrets.S3_UPLOAD_BUCKET }}
#          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
#          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
#        run: |
#          aws s3 rm --recursive --region ap-southeast-1 s3://$S3_UPLOAD_BUCKET
#          aws s3 cp --recursive --region ap-southeast-1 .output/public/ s3://$S3_UPLOAD_BUCKET
#
#      - name: Invalidate CloudFront
#        uses: chetan/invalidate-cloudfront-action@v2
#        env:
#          DISTRIBUTION: ${{ secrets.AWS_CLOUD_FRONT_ID }}
#          PATHS: "/*"
#          AWS_REGION: "ap-southeast-1"
#          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
#          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
