if ('Notification' in window) {
    const permission = await Notification.requestPermission()
    console.log(permission)
    if (permission === 'granted') {
        const notification = new Notification('Notificación de prueba', {
            body: 'Hola mundo',
        })
        notification.onclick = () => {
            console.log('Click en la notificación')
        }
    }

    if (permission === 'denied') {
        console.log('El usuario no quiere notificaciones')
        return
    }
}
