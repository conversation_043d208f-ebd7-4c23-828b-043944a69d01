<template>
    <div>
        <NuxtLayout>
            <NuxtPage />
            <TheDrawerUser />
            <div data-type="_mgwidget" data-widget-id="1603194"></div>
        </NuxtLayout>
    </div>
</template>

<script setup>
import 'flowbite'
import { onMounted, watch, nextTick } from 'vue'
import { initFlowbite } from 'flowbite'
import 'vue-loading-overlay/dist/css/index.css'
import { useAuthStore } from '~~/stores/auth'
import { storeToRefs } from 'pinia'
import { PROTECTED_ROUTES } from '~/middleware/auth.global'
import TheDrawerUser from '~/components/DefaultLayout/TheDrawerUser.vue'
import '@sjmc11/tourguidejs/src/scss/tour.scss'
import { useAppStore } from '~~/stores/app'
import { useI18n } from 'vue-i18n'
import { ModalsContainer, useModal } from 'vue-final-modal'
import SessionExpired from './base-components/SessionExpired.vue'
import { useNotificationsStore } from '~~/stores/notifications'
import { TourGuideClient } from '@sjmc11/tourguidejs/src/Tour'
import { useTranslateStore } from '~~/stores/translate'
import { useHistoryStore } from '~~/stores/history'
import { usePaymentsStore } from '~~/stores/payments'

const router = useRouter()

const { open, close, options } = useModal({
    component: SessionExpired,
    attrs: {
        onClose() {
            isExpired.value = false
            close()
            router.push('/signin')
        },
    },
})
const notificationsStore = useNotificationsStore()
const appStore = useAppStore()
const translateStore = useTranslateStore()
const { inputFile, customPrompt } = storeToRefs(translateStore)
const { t, locale } = useI18n({ useScope: 'global' })

const authStore = useAuthStore()
const { user, isExpired, showAds } = storeToRefs(authStore)
const { tg, dropdownLanguage } = storeToRefs(appStore)
const historyStore = useHistoryStore()
const paymentStore = usePaymentsStore()
onMounted(() => {
    nextTick(() => {
        initFlowbite()

        const stepDocument = [
            {
                content: t('tour_guide.select_translate_document'),
                title: t('Translate document'),
                target: '#nav-item-translate-document',
                order: 1,
                group: 'translate-document',
            },
            {
                content: user.value ? t('tour_guide.translate_document') : t('tour_guide.translate_document_guest'),
                title: t('Translate document'),
                order: 2,
                group: 'translate-document',
                beforeEnter: async () => {
                    return new Promise(async (resolve) => {
                        await router.push('/documents')
                        await tg.value?.refresh()
                        await tg.value?.refreshDialog()
                        await tg.value?.updatePositions()
                        if (!user.value) {
                            tg.value?.setOptions({
                                nextLabel: t('Login as Guest'),
                            })
                        }

                        return resolve(true)
                    })
                },
                afterLeave: async (c, n) => {
                    if (c.order == n.order) return
                    return new Promise(async (resolve) => {
                        if (!user.value) {
                            tg.value?.setOptions({
                                nextLabel: t('Next'),
                            })
                            authStore.loginAsGuest()
                        }
                        return resolve(true)
                    })
                },
            },
        ]

        const stepHistory = [
            {
                content: t('tour_guide.select_translate_history'),
                title: t('Translation history'),
                target: '#nav-item-translation-history',
                order: 1,
                group: 'translate-history',
            },
            {
                content: user.value ? t('tour_guide.translate_history') : t('tour_guide.translate_history_guest'),
                title: t('Translation history'),
                order: 2,
                group: 'translate-history',
                beforeEnter: async () => {
                    return new Promise(async (resolve) => {
                        await router.push('/history')

                        if (!user.value) {
                            tg.value?.setOptions({
                                nextLabel: t('Login as Guest'),
                            })
                        }
                        await tg.value?.refresh()
                        await tg.value?.refreshDialog()
                        await tg.value?.updatePositions()

                        return resolve(true)
                    })
                },
                afterLeave: async (c, n) => {
                    if (c.order == n.order) return
                    return new Promise(async (resolve) => {
                        if (!user.value) {
                            tg.value?.setOptions({
                                nextLabel: t('Next'),
                            })
                            authStore.loginAsGuest()
                        }
                        return resolve(true)
                    })
                },
            },
        ]

        const steps = [
            {
                content: t('tour_guide.main_menu'),
                title: t('This is main menu'),
                target: '#nav-menu',
                order: 1,
                group: 'navigation',
            },
            {
                content: t('tour_guide.drawer_menu'),
                title: t('This is drawer menu'),
                target: '#drawer-menu',
                order: 2,
                group: 'navigation',
                beforeEnter: async () => {
                    return new Promise(async (resolve) => {
                        await appStore.setShowAppDrawer(false)
                        await tg.value?.updatePositions()
                        return resolve(true)
                    })
                },
            },
            {
                content: t('tour_guide.drawer_menu_list'),
                title: t('This is drawer menu list'),
                target: '#drawer-menu-list',
                order: 3,
                group: 'navigation',
                beforeEnter: async () => {
                    return new Promise(async (resolve) => {
                        await appStore.setShowAppDrawer(true)
                        await tg.value?.updatePositions()
                        return resolve(true)
                    })
                },
                afterLeave: async () => {
                    return new Promise(async (resolve) => {
                        await appStore.setShowAppDrawer(false)
                        return resolve(true)
                    })
                },
            },
            //Language
            {
                content: t('tour_guide.language_website'),
                title: t('Language of website'),
                target: '#website-language-select',
                order: 1,
                group: 'language',
            },
            {
                content: t('tour_guide.select_language_website'),
                title: t('Select language'),
                target: '#dropdownLanguage',
                order: 2,
                group: 'language',
                beforeEnter: async () => {
                    return new Promise(async (resolve) => {
                        await dropdownLanguage.value?.show()
                        await tg.value?.updatePositions()
                        return resolve(true)
                    })
                },
                afterLeave: async () => {
                    return new Promise(async (resolve) => {
                        await dropdownLanguage.value?.hide()
                        await tg.value?.updatePositions()
                        return resolve(true)
                    })
                },
            },
            {
                content: t('tour_guide.select_translate_text'),
                title: t('Translate text'),
                target: '#nav-item-translate-text',
                order: 1,
                group: 'translate-text',
            },
            {
                content: t('tour_guide.translate_text'),
                title: t('Translate text'),
                order: 2,
                group: 'translate-text',
                beforeEnter: async () => {
                    return new Promise(async (resolve) => {
                        await router.push('/')
                        await tg.value?.refresh()
                        await tg.value?.refreshDialog()
                        await tg.value?.updatePositions()
                        return resolve(true)
                    })
                },
            },
            ...stepDocument,
            ...stepHistory,
        ]
        tg.value = new TourGuideClient({
            steps: steps,
            nextLabel: t('Next'),
            prevLabel: t('Previous'),
            finishLabel: t('Finish'),
            debug: true,
            showStepDots: false,
            exitOnClickOutside: false,
            hidePrev: true,
        })

        tg.value.onAfterExit(() => {
            // tg.value?.stop()
            document.documentElement.style.overflow = 'auto'
            appStore.setShowAppDrawer(false)
            authStore.logoutGuest()
            notificationsStore.removeFakeNotification()
            inputFile.value = null
            customPrompt.value = null
        })

        let script = document.createElement('script')
        script.innerHTML = `(function(w,q){w[q]=w[q]||[];w[q].push(["_mgc.load"])})(
        window,"_mgq"
    );`
        document.body.appendChild(script)
    })

    paymentStore.getAllProductsAndPlans()
})

useHead({
    bodyAttrs: {
        class: 'bg-gray-50 dark:bg-gray-900 max-w-screen overflow-x-hidden',
    },
})

watch(user, () => {
    if (!user.value) {
        const route = useRoute()
        const router = useRouter()
        if (PROTECTED_ROUTES.includes(route.name)) {
            router.push('/signin')
        }
    }
})

watch(isExpired, () => {
    if (isExpired.value) {
        open()
    }
})

useSeoMeta({
    title: 'DocTransGPT | Professional business translator',
    ogTitle: 'DocTransGPT | Professional business translator',
    description:
        "DocTransGPT is a professional business translator that helps you translate your documents, websites, and more. It is powered by GPT-3.5 and GPT-4 models. With DocTransGPT, you can customize translations to suit your specific needs and provide feedback on adjustments you'd love to see.",
    ogDescription:
        "DocTransGPT is a professional business translator that helps you translate your documents, websites, and more. It is powered by GPT-3.5 and GPT-4 models. With DocTransGPT, you can customize translations to suit your specific needs and provide feedback on adjustments you'd love to see.",
    ogImage: 'https://doctransgpt.com/_nuxt/logo.4dd956f9.png',
})
</script>

<style>
.vfm__content {
    width: 100%;
}
</style>
