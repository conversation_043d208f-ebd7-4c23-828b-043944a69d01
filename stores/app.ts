import { defineStore } from 'pinia'
import { TourGuideClient } from '@sjmc11/tourguidejs/src/Tour'

export const useAppStore = defineStore('app', {
    state: () => ({
        showTermsOfServiceModal: false,
        acceptedTermsOfService: false,
        darkMode: localStorage.getItem('darkMode') === 'true' ? true : false,
        showAppDrawer: false,
        showUserDrawer: false,
        showNotificationsDropdown: false,
        contactEmail: '<EMAIL>',
        chatGPTVersion: localStorage.getItem('chatGPTVersion') || "gpt-4o-mini",
        // chatGPTVersion: 'gpt-4o',
        appVersion: '0.0.1',
        tg: {} as TourGuideClient,
        dropdownLanguage: null,
        showNotificationPopup: false,
        dropdownVersion: null,
    }),

    getters: {},

    actions: {
        setShowNotificationPopup(value: boolean) {
            this.showNotificationPopup = value || false
            localStorage.setItem('seenNotificationPopup', value.toString())
        },
        setShowTermsOfServiceModal(value: boolean) {
            this.showTermsOfServiceModal = value || false
        },
        toggleDarkMode() {
            const newDarkMode = !this.darkMode
            this.darkMode = newDarkMode || false
            localStorage.setItem('darkMode', newDarkMode.toString())
        },
        setShowAppDrawer(value: boolean) {
            this.showAppDrawer = value || false
        },
        setShowUserDrawer(value: boolean) {
            this.showUserDrawer = value || false
        },
        setShowNotificationsDropdown(value: boolean) {
            this.showNotificationsDropdown = value || false
        },
        changeChatGPTVersion(value: string) {
            this.chatGPTVersion = value || 'gpt-4o-mini'
            localStorage.setItem('chatGPTVersion', value)
            this.dropdownVersion?.hide()
        },
    },
})
