import { defineStore } from 'pinia'
import { getDatabase, ref, onValue, update, query, orderByChild, startAt } from 'firebase/database'
import { useFirebase } from '~/composables/useFirebase'
import _ from 'lodash'

interface DownloadFileResponse {
    url: string
}

interface Notification {
    created_at: string
    document_name: string
    message: string
    seen: boolean
    translation_history_id: number
    uuid: string
    success?: boolean
}

export const useNotificationsStore = defineStore('notifications', {
    state: () => ({
        notifications: [],
        isLoading: false,
        userUUID: '',
    }),

    getters: {
        hasUnreadNotifications: (state) => {
            return Object.values(state.notifications).some((notification: any) => !notification.seen)
        },
        sortedNotifications: (state) => {
            const temp = [] as Notification[]
            Object.entries(state.notifications).forEach(([key, value]) => {
                if ((value as Notification).translation_history_id) {
                    temp.push({ ...(value as Notification), uuid: key })
                }
            })
            // only get notifications in the last 30 days
            return _.orderBy(temp, ['created_at'], ['desc']).filter((notification) => {
                const today = new Date()
                const notificationDate = new Date(notification.created_at)
                const diffTime = Math.abs(today.getTime() - notificationDate.getTime())
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
                return diffDays <= 30
            })
        },
    },

    actions: {
        async getNotifications(userUUID: string) {
            this.isLoading = true
            this.userUUID = userUUID
            const { firebaseApp } = useFirebase()
            const db = getDatabase(firebaseApp)
            // Calculate the date 30 days ago
            const currentDate = new Date()
            const thirtyDaysAgo = new Date()
            thirtyDaysAgo.setDate(currentDate.getDate() - 30)
            const formattedThirtyDaysAgo = thirtyDaysAgo.toISOString().slice(0, 19).replace('T', ' ')
            //startAt and endAt are used to get the last 30 days of notifications
            const notificationsRef = query(
                ref(db, 'notification/' + userUUID),
                orderByChild('created_at'),
                startAt(formattedThirtyDaysAgo)
            )
            onValue(notificationsRef, (snapshot) => {
                const data = snapshot.val()
                this.notifications = { ...data }
                this.isLoading = false
            })
        },

        async markAsRead(notificationUUID: string) {
            const { firebaseApp } = useFirebase()
            const db = getDatabase(firebaseApp)
            const notificationRef = ref(db, 'notification/' + this.userUUID + '/' + notificationUUID)
            await update(notificationRef, { seen: true })
        },

        async markAllAsRead() {
            const { firebaseApp } = useFirebase()
            const db = getDatabase(firebaseApp)
            for (const notification of this.sortedNotifications) {
                const notificationRef = ref(db, 'notification/' + this.userUUID + '/' + notification.uuid)
                await update(notificationRef, { seen: true })
            }
        },

        async downloadFile(id: string) {
            const { data }: { data: Ref<DownloadFileResponse> } = await useAPI('download-file/' + id, {
                method: 'GET',
                // lazy: true,
                server: false,
            })
            if (data.value?.url) {
                //download file by creating a link and clicking it
                const link = document.createElement('a')
                link.href = data.value.url
                link.setAttribute('download', '')
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link)
            }
        },

        addFakeNotification() {
            const notification = {
                created_at: new Date().toISOString(),
                document_name: 'another_file.pdf',
                message: 'Your document has been translated',
                seen: false,
                translation_history_id: 1,
                uuid: 'fake-notification-uuid',
                success: true,
            }
            this.notifications = { ...this.notifications, [notification.uuid]: notification }
        },

        removeFakeNotification() {
            const notifications = { ...this.notifications }
            delete notifications['fake-notification-uuid']
            this.notifications = notifications
        },
    },
})
