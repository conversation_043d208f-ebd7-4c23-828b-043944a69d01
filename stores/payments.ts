import { defineStore } from 'pinia'
import { useAuthStore } from './auth'
import { storeToRefs } from 'pinia'
const TOKEN_UNIT = 1000000
const TOKEN_UNIT_PRICE = 6 //$
export const TOKEN_PROD_ID = 'TP0001'

interface LoadingObject {
    [key: string]: boolean
}

interface PaypalCreateOrderResponse {
    success: boolean
    order_id?: string
    order_uuid?: string
    approval_url?: string
    message: string
    error_code?: string
}

interface PaypalCreateSubscriptionResponse {
    success: boolean
    subscription_id?: string
    order_uuid?: string
    approval_url?: string
    message: string
    error_code?: string
}

interface PaypalCompleteOrderResponse {
    success: boolean
}

export interface PaymentHistory {
    id: number
    uuid: string
    user_id: string
    product_id: string
    status: string
    amount: number
    amount_divide_100: number
    quantity: number
    type: string
    payment_method: string
    created_at: string
    updated_at: string
    isSuccess?: boolean
}

interface OrderHistoriesResponse {
    success: boolean
    total: number
    result: Array<PaymentHistory> | []
}

export const usePaymentsStore = defineStore('payments', {
    state: () => ({
        loading: {} as LoadingObject,
        showPaymentModal: false,
        quickTopUpList: [1, 2, 5, 7, 10, 12, 15, 17, 20].map((num) => ({
            tokens: num * TOKEN_UNIT,
            price: num * TOKEN_UNIT_PRICE,
            quantity: num,
        })),
        tokenUnitPrice: TOKEN_UNIT_PRICE,
        topUpTokens: 69000000,
        tokenUnit: TOKEN_UNIT,
        orderUUID: '' as String,
        orderID: '' as String,
        // paymentHistories: [
        //     {
        //         id: 1,
        //         date: '2023-08-01 12:00:00',
        //         tokens: 10000000,
        //         price: 60,
        //         status: 'paid',
        //         txnId: '1234567890',
        //     },
        //     {
        //         id: 2,
        //         date: '2023-08-01 12:00:00',
        //         tokens: 10000000,
        //         status: 'failed',
        //         txnId: '1234567890',
        //     },
        //     {
        //         id: 3,
        //         date: '2023-08-01 12:00:00',
        //         tokens: 10000000,
        //         status: 'processing',
        //         txnId: '1234567890',
        //     },
        // ],
        paymentHistories: [] as Array<PaymentHistory> | [],
        paymentHistoriesTotal: 0,
        error: {},
        fetchOptions: {
            items_per_page: 5,
            page: 1,
        },
        isSubscribed: false,
        products: [] as Array<any>,
        modelOptions: ['gpt-4'],
    }),

    getters: {
        topUpAmount: (state) => (state.topUpTokens / state.tokenUnit) * state.tokenUnitPrice,
        bonus: () => {
            return 50
        },
        planList: (state) => {
            return state.products
                .filter((item) => item.type === 1)
                .map((item) => {
                    return {
                        id: item.id,
                        product_id: item.id,
                        price: +item.price_divide_100,
                        token: +item.base_token,
                        bonus: +(item.bonus_token / item.base_token) * 100,
                        plan_id: item.paypal_plan_id,
                        ...item,
                    }
                })
        },
        usableModelList: (state) => {
            const authStore = useAuthStore()
            const { user } = authStore
            const uableModels = state.products.find(
                (item) => item.type === 1 && (user?.user_plan?.product?.id === item.id)
            )?.usable_gpt_versions
            return uableModels || ['gpt-4o-mini']
        },
    },

    actions: {
        async createPaypalOrder(payload: object) {
            this.loading['createPaypalOrder'] = true
            this.error = {}
            const { data, error }: { data: Ref<PaypalCreateOrderResponse>; error: any } = await useAPI(
                'order/paypal/create',
                {
                    method: 'POST',
                    server: false,
                    body: payload,
                }
            )
            this.loading['createPaypalOrder'] = false
            if (data.value?.success) {
                this.orderUUID = data.value?.order_uuid
                this.orderID = data.value?.order_id
                return data.value?.order_id
            }

            if (error.value && error.value.data) {
                this.error['createPaypalOrder'] = error.value.data
                return false
            }

            return false
        },

        async approvePaypalOrder() {
            try {
                this.loading['approvePaypalOrder'] = true
                this.error = {}
                const { data, error }: { data: Ref<PaypalCompleteOrderResponse>; error: any } = await useAPI(
                    `order/paypal/${this.orderUUID}/complete`,
                    {
                        method: 'PUT',
                        server: false,
                        query: {
                            order_uuid: this.orderUUID,
                        },
                    }
                )
                this.loading['approvePaypalOrder'] = false
                if (data.value) {
                    return data.value
                }

                if (error.value && error.value.data) {
                    this.error['approvePaypalOrder'] = error.value.data
                    return false
                } else {
                    this.error['approvePaypalOrder'] = {
                        message: 'Something went wrong',
                        error_code: 'SYSTEM_ERROR',
                    }
                }

                return false
            } catch (error) {
                this.loading['approvePaypalOrder'] = false
                this.error['approvePaypalOrder'] = {
                    message: 'Something went wrong',
                    error_code: 'SYSTEM_ERROR',
                }
                return false
            }
        },

        async cancelPaypalOrder() {
            this.loading['cancelPaypalOrder'] = true
            this.error = {}
            const { data }: { data: Ref<string> } = await useAPI(`order/paypal/${this.orderUUID}/cancel`, {
                method: 'PUT',
                server: false,
            })
            this.loading['cancelPaypalOrder'] = false
            if (data.value) {
                return data.value
            }
            return false
        },

        async getOrderHistories() {
            this.loading['getOrderHistories'] = true

            const { data }: { data: Ref<OrderHistoriesResponse> } = await useAPI(`orders`, {
                method: 'GET',
                server: false,
                query: this.fetchOptions,
            })
            this.loading['getOrderHistories'] = false
            if (data.value) {
                this.paymentHistories = data.value.result.map((item) => {
                    return {
                        ...item,
                        isSuccess: item.status === '2',
                    }
                })
                this.paymentHistoriesTotal = data.value.total
                return data.value
            }
            return false
        },

        async createSubscription(payload: object) {
            this.loading['createSubscription'] = true

            const { data, error }: { data: Ref<PaypalCreateSubscriptionResponse>; error: any } = await useAPI(
                'subscription/paypal/create',
                {
                    method: 'POST',
                    server: false,
                    body: payload,
                }
            )
            this.loading['createSubscription'] = false
            if (data.value?.success) {
                this.orderUUID = data.value?.subscription_id
                return data.value?.subscription_id
            }

            if (error.value && error.value.data) {
                this.error['createSubscription'] = error.value.data
                return false
            }

            return false
        },

        async cancelSubscription(payload: object) {
            this.loading['cancelSubscription'] = true
            this.error = {}
            const { data, error }: { data: Ref<PaypalCreateSubscriptionResponse>; error: any } = await useAPI(
                `subscription/paypal/${payload.subscription_id}/cancel`,
                {
                    method: 'PUT',
                    server: false,
                    body: payload,
                }
            )
            this.loading['cancelSubscription'] = false
            if (data.value?.success) {
                return true
            }

            if (error.value && error.value.data) {
                this.error['cancelSubscription'] = error.value.data
                return false
            }

            return false
        },

        async getAllProductsAndPlans() {
            this.loading['getAllProductsAndPlans'] = true
            this.error = {}
            const { data, error }: { data: Ref<PaypalCreateSubscriptionResponse>; error: any } = await useAPI(
                `products`,
                {
                    method: 'GET',
                    server: false,
                }
            )
            this.loading['getAllProductsAndPlans'] = false
            if (data.value?.success) {
                this.products = data.value?.result
                return true
            }

            if (error.value && error.value.data) {
                this.error['getAllProductsAndPlans'] = error.value.data
                return false
            }

            return false
        },
    },
})
