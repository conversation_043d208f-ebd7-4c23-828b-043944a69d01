import { defineStore } from 'pinia'
import { useAPI } from '~/composables/useAPI'
import { useTranslateStore } from './translate'
import { useRoute } from 'vue-router'
const route = useRoute()

interface History {
    id: number
    type: string
    origin_lang: string
    target_lang: string
    domain: string
    tone: string
    writing_style: string
    trans_input: string
    trans_result: string
    used_token: number
    created_at: string
}

interface HistoryResponse {
    success: boolean
    total: number
    result: History[]
}

const FAKE_HISTORIES: History[] = [
    {
        id: 99999999,
        uuid: '6c924fd0-5f9b-11ee-0000-0a58a9feac02',
        origin_lang: 'english',
        target_lang: 'vietnamese',
        domain: 'Art & Design',
        tone: 'Authoritative',
        writing_style: 'Academic',
        trans_input:
            'This is a very long text. You can not see the end of it if you do not expand the box. This is a very long text. You can not see the end of it if you do not expand the box. This is a very long text. You can not see the end of it if you do not expand the box. This is a very long text. You can not see the end of it if you do not expand the box. This is a very long text. You can not see the end of it if you do not expand the box. This is a very long text. You can not see the end of it if you do not expand the box. This is a very long text. You can not see the end of it if you do not expand the box.',
        trans_result:
            'Đây là một đoạn văn rất dài. Bạn không thể nhìn thấy phần cuối nếu bạn không mở rộng hộp. Đây là một đoạn văn rất dài. Bạn không thể nhìn thấy phần cuối nếu bạn không mở rộng hộp. Đây là một đoạn văn rất dài. Bạn không thể nhìn thấy phần cuối nếu bạn không mở rộng hộp. Đây là một đoạn văn rất dài. Bạn không thể nhìn thấy phần cuối nếu bạn không mở rộng hộp. Đây là một đoạn văn rất dài. Bạn không thể nhìn thấy phần cuối nếu bạn không mở rộng hộp. Đây là một đoạn văn rất dài. Bạn không thể nhìn thấy phần cuối nếu bạn không mở rộng hộp. Đây là một đoạn văn rất dài. Bạn không thể nhìn thấy phần cuối nếu bạn không mở rộng hộp.',
        type: 'translate-text',
        used_token: 122,
        used_model: 'gpt-3.5-turbo',
        status: 2,
        status_desc: '',
        status_percentage: 1,
        error_message: '',
        rating: '',
        rating_content: '',
        custom_prompt: '',
        created_at: '2023-09-30T14:12:44',
        updated_at: '2023-09-30T14:12:45',
        file_size: 0,
        file_password: '',
    },
]

const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))
export const useHistoryStore = defineStore('history', {
    state: () => ({
        filterBy: route?.query?.filterBy || 'all',
        isLoading: false,
        isError: false,
        defaultItemPerPage: 10,
        totalPage: 1,
        currentPage: 1,
        // histories: [] as History[],
        filteredHistories: [] as History[],
        historyUUIDTobeDeleted: '',
        fakeHistories: FAKE_HISTORIES,
    }),

    getters: {},

    actions: {
        setFilterBy(filterBy: string) {
            this.filterBy = filterBy
        },

        async filterHistories() {
            this.isLoading = true
            this.isError = false
            this.filteredHistories = []
            this.totalPage = 1
            let query = {
                filter_by: this.filterBy,
                items_per_page: 10,
                page: this.currentPage,
            }

            const { data }: { data: Ref<HistoryResponse> } = await useAPI('histories', {
                method: 'GET',
                query: query,
                // lazy: true,
                server: false,
            })
            this.isLoading = false

            if (data.value?.success) {
                this.filteredHistories = data.value.result
                this.totalPage = Math.ceil(data.value.total / this.defaultItemPerPage)
                return true
            }

            this.isError = true
            return false
        },

        async fetchHistoryById(translation_uuid: string) {
            this.isLoading = true
            this.isError = false
            this.filteredHistories = []
            this.totalPage = 1
            const { data }: { data: Ref<History> } = await useAPI('history/' + translation_uuid, {
                method: 'GET',
                // lazy: true,
                server: false,
            })
            this.isLoading = false

            if (data.value) {
                this.filteredHistories = [data.value]
                this.totalPage = 1
                return true
            }

            this.isError = true
            return false
        },

        async deleteHistory(id: any, uuid: string) {
            this.isLoading = true
            this.isError = false

            const { data }: { data: Ref<HistoryResponse> } = await useAPI('history/' + id, {
                method: 'DELETE',
                // lazy: true,
                server: false,
            })
            this.isLoading = false

            if (data.value?.success) {
                const translateStore = useTranslateStore()
                const { resultUuid } = translateStore

                if (resultUuid === uuid) {
                    translateStore.clearTranslateText()
                }
                return true
            }

            this.isError = true
            return false
        },
        async deleteAllHistory() {
            this.isLoading = true
            this.isError = false
            const deleteAllUrl = this.filterBy === 'all' ? 'histories/all' : 'histories/all/' + this.filterBy
            const { data }: { data: Ref<HistoryResponse> } = await useAPI(deleteAllUrl, {
                method: 'DELETE',
                // lazy: true,
                server: false,
            })
            this.isLoading = false

            if (data.value?.success) {
                return true
            }

            this.isError = true
            return false
        },
        async rateHistory(history_uuid: string, payload: object) {
            let publicPrefix = ''
            if (!localStorage.getItem('access_token')) {
                // User is not logging in then use public API (limited translate)
                publicPrefix = 'public/'
            }

            const { data }: { data: Ref<HistoryResponse> } = await useAPI(
                publicPrefix + 'history-feedback/' + history_uuid,
                {
                    method: 'PUT',
                    // lazy: true,
                    body: payload,
                    server: false,
                }
            )

            return !!data.value?.success
        },
    },
})
