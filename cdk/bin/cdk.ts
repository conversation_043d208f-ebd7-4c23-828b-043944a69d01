#!/usr/bin/env node
import 'source-map-support/register';
import * as cdk from 'aws-cdk-lib';
import { CdkStack } from '../lib/cdk-stack';

const app = new cdk.App();

const inputContext = app.node.tryGetContext('contxt')
const globalContext = app.node.tryGetContext('global')
const context = app.node.tryGetContext(inputContext)
const env = context['env']


new CdkStack(app, 'FrontendStack', context ,{
  stackName: `${globalContext.prefix}-${env.environment}-frontend-stack`,
  description: "Create Frontend stack",
  env: {
    account: `${env.account}`,
    region: `${env.region}`
  }
});