version: 0.2

phases:
  install:
    runtime-versions:
      nodejs: 18
    commands:
    # Install dependencies for both site content and for CDK
    # - npm install -g npm@latest
    - npm install -g aws-cdk
    - npm install -g pnpm
    - echo $ENV_FILE | tr " " "\n" > .env
    - cd cdk
    - npm install
    - cd ..
    - yarn install
  build:
    commands:
    # Compile the site content
    - npm run generate
    # Deploy via the CDK
    - cd cdk
    - npm run build
    - cdk deploy FrontendStack -c contxt=$CONTXT_ENV --require-approval never

    # Clear cache
    # - echo "[+] Clear cache ..."
    - aws cloudfront create-invalidation --distribution-id $DISTRIBUTION_ID --paths '/*'

cache:
  paths:
  - '/root/.npm/**/*'
