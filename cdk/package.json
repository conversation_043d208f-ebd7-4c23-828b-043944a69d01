{"name": "cdk", "version": "0.1.0", "bin": {"cdk": "bin/cdk.js"}, "scripts": {"build": "tsc", "watch": "tsc -w", "test": "jest", "cdk": "cdk"}, "devDependencies": {"@types/jest": "^29.5.1", "@types/node": "20.1.7", "jest": "^29.5.0", "ts-jest": "^29.1.0", "aws-cdk": "2.86.0", "ts-node": "^10.9.1", "typescript": "~5.1.3"}, "dependencies": {"aws-cdk-lib": "2.86.0", "constructs": "^10.0.0", "source-map-support": "^0.5.21"}}