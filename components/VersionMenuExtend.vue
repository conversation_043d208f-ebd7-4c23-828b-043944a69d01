<template>
  <a
    id="dropdownRadioHelperButton"
    class="flex flex-inline items-center cursor-pointer dark:text-gray-200 px-2 py-2 rounded-full hover:text-gray-100 bg-gray-200 hover:bg-primary-600 dark:bg-gray-700 dark:hover:bg-primary-800 dark:hover:text-white"
  >
    <svg
      class="w-3 h-3 group-hover:text-gray-500 dark:text-gray-50 dark:group-hover:text-gray-300"
      aria-hidden="true"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 10 6"
    >
      <path
        stroke="currentColor"
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="m1 1 4 4 4-4"
      />
    </svg>
  </a>
  <!-- Dropdown menu -->
  <div
    id="dropdownRadioHelper"
    class="z-30 hidden bg-white divide-y divide-gray-100 rounded-lg shadow w-96 dark:bg-gray-700 dark:divide-gray-600"
  >
    <ul class="p-3 space-y-1 text-sm text-gray-700 dark:text-gray-200">
      <li v-for="version in versions" @click="changeAIModel(version.value)">
        <div class="relative flex p-2 rounded hover:bg-gray-100 dark:hover:bg-gray-600">
          <div class="flex items-center h-5">
            <input
              name="helper-radio"
              type="radio"
              :value="version.value"
              :checked="version.value === chatGPTVersion"
              class="w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-700 dark:focus:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500"
            />
          </div>
          <div class="ms-2 text-sm text-left">
            <label class="font-medium text-gray-900 dark:text-gray-300">
              <div>
                {{ $t(version.label) }}
              </div>
              <div class="flex flex-col gap-1">
                <p
                  v-for="desc in version.descriptions"
                  class="text-xs font-normal text-gray-500 dark:text-gray-300"
                >
                  {{ $t(desc) }}
                </p>
                <p v-if="!version.usable" class="text-xs font-normal text-yellow-400">
                  {{ $t("Update your plan to use this model.") }}
                </p>
              </div>
            </label>
          </div>
          <div
            v-if="version.isNew"
            class="absolute inline-flex items-center justify-center h-5 px-1 text-[7px] rounded-md font-bold text-white bg-red-500 border-2 border-white top-2 end-2 dark:border-gray-900 animate__animated animate__tada animate__infinite animate__slower"
          >
            {{ $t("New !") }}
          </div>
        </div>
      </li>
    </ul>
  </div>
</template>

<script lang="ts" setup>
import { storeToRefs } from "pinia";
import { useAppStore } from "~~/stores/app";
import TheTooltip from "~/base-components/TheTooltip.vue";
import { usePaymentsStore } from "~~/stores/payments";
import { Dropdown } from "flowbite";
import IconGpt35 from "~/components/Icon/IconGpt35.vue";
import IconGpt4 from "~/components/Icon/IconGpt4.vue";
import IconGpt4Turbo from "~/components/Icon/IconGpt4Turbo.vue";
import IconGpt4o from "~/components/Icon/IconGpt4o.vue";
import IconGpt4oMini from "~/components/Icon/IconGpt4oMini.vue";

const paymentStore = usePaymentsStore();

const { usableModelList } = storeToRefs(paymentStore);
const appStore = useAppStore();

const { chatGPTVersion, dropdownVersion } = storeToRefs(appStore);

const config = useRuntimeConfig();
const isGPT4Enabled = config.public.NUXT_MODEL_GPT_4;

const changeAIModel = (model: string) => {
  if (usableModelList.value.includes(model)) {
    appStore.changeChatGPTVersion(model);
  } else {
    navigateTo("/pricing-plans");
  }
};
const versions = computed(() => {
  return [
    {
      value: "gpt-4o",
      label: "GPT-4o",
      descriptions: [
        "GPT-4o has the best vision and performance across non-English languages of any of our models.",
        "For text in English: 1,000 words ~ 30,000 tokens.",
      ],
      usable: usableModelList.value.includes("gpt-4o"),
      icon: IconGpt4o,
      isNew: true,
      show: ["gpt-4-turbo", "gpt-3.5"].includes(chatGPTVersion.value),
    },
  ].filter((version) => version.show);
});
const currentVersion = computed(() => {
  return (
    versions.value.find((version) => version.value === chatGPTVersion.value) ||
    versions.value[0]
  );
});
onMounted(() => {
  if (process.client) {
    const $targetEl = document.getElementById("dropdownRadioHelper");
    const $triggerEl = document.getElementById("dropdownRadioHelperButton");
    const options = {
      placement: "bottom",
      triggerType: "click",
      // offsetSkidding: 0,
      // offsetDistance: 10,
      delay: 100,
      onHide: () => {},
      onShow: () => {},
      onToggle: () => {},
    };
    dropdownVersion.value = new Dropdown($targetEl, $triggerEl, options);
  }
});
</script>
