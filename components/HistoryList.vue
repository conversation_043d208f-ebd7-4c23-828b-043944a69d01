<template>
    <div>
        <loading loader="bars" height="35" v-model:active="isLoading" :is-full-page="true" />

        <div
            v-if="!filteredHistories.length"
            class="flex-1 text-center text-gray-600 dark:text-gray-400 font-thin pt-10"
        >
            {{ $t('No history found.') }}
        </div>
        <ol class="relative border-l border-gray-200 dark:border-gray-700 list-none pl-0">
            <component
                v-show="tg.isVisible && !tg.isFinished('translate-history')"
                :key="rowFake.id"
                v-for="rowFake in fakeHistories"
                :is="historyItemComponents[rowFake.type]"
                v-bind="rowFake"
            />
            <component
                :key="row.id"
                v-for="row in filteredHistories"
                :is="historyItemComponents[row.type]"
                v-bind="row"
                @delete="onDeleteHistory(row.id, row.uuid)"
                @download="onDownloadFile(row.id)"
                @rated="onRated"
            />
        </ol>

        <div class="flex flex-inline w-max mx-auto">
            <!-- Previous Button -->
            <a
                v-if="currentPage > 1"
                @click="previousPage"
                href="#"
                class="inline-flex items-center px-4 py-2 mr-3 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white"
            >
                <svg
                    aria-hidden="true"
                    class="w-5 h-5 mr-2"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path
                        fill-rule="evenodd"
                        d="M7.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l2.293 2.293a1 1 0 010 1.414z"
                        clip-rule="evenodd"
                    ></path>
                </svg>
                {{ $t('Previous') }}
            </a>
            <a
                v-if="currentPage < totalPage"
                @click="nextPage"
                href="#"
                class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white"
            >
                {{ $t('Next') }}
                <svg
                    aria-hidden="true"
                    class="w-5 h-5 ml-2"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path
                        fill-rule="evenodd"
                        d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z"
                        clip-rule="evenodd"
                    ></path>
                </svg>
            </a>
        </div>

        <DeleteConfirmation
            :title="$t('Are you sure you want to delete this item?')"
            :isOpen="showDeleteConfirmation"
            @cancel="onCancelDeleteHistory"
            @confirm="onConfirmDeleteHistory"
        />
    </div>
</template>

<script setup lang="ts">
import { ModalsContainer, useModal } from 'vue-final-modal'
import { ref, onMounted, watch } from 'vue'
import { storeToRefs } from 'pinia'
import { useHistoryStore } from '~~/stores/history'
import { useTranslateStore } from '~~/stores/translate'
import { useNotificationsStore } from '~~/stores/notifications'
import { useAppStore } from '~~/stores/app'
import Loading from 'vue-loading-overlay'
import HistoryItemText from '~/components/HistoryItemText.vue'
import HistoryItemDocuments from '~/components/HistoryItemDocuments.vue'
import DeleteConfirmation from '~/base-components/DeleteConfirmation.vue'
import { useI18n } from 'vue-i18n'
const { t } = useI18n({ useScope: 'global' })
const route = useRoute()
const appStore = useAppStore()
const { tg } = storeToRefs(appStore)
const { open, close } = useModal({
    component: DeleteConfirmation,
    attrs: {
        title: t('Are you sure you want to delete this item?'),
        async onConfirm() {
            await historyStore.deleteHistory(historyTobeDeleted.value, historyUUIDTobeDeleted.value)
            await historyStore.filterHistories()
            close()
        },
        onCancel() {
            close()
        },
    },
})

const historyTobeDeleted = ref()
const historyStore = useHistoryStore()
const notificationsStore = useNotificationsStore()
const { filteredHistories, isLoading, totalPage, currentPage, historyUUIDTobeDeleted, fakeHistories } =
    storeToRefs(historyStore)

const onDeleteHistory = (val: number, uuid: string) => {
    historyTobeDeleted.value = val
    historyUUIDTobeDeleted.value = uuid
    open()
}

const translateStore = useTranslateStore()
const { resultUuid, liked, disLiked } = storeToRefs(translateStore)
const onRated = (payload: any) => {
    if (resultUuid.value === payload.uuid) {
        liked.value = payload.liked
        disLiked.value = payload.disLiked
    }
}

const historyItemComponents = {
    'translate-text': HistoryItemText,
    'translate-document': HistoryItemDocuments,
}

const previousPage = async () => {
    currentPage.value = currentPage.value > 1 ? currentPage.value - 1 : 1
    await historyStore.filterHistories()
}

const nextPage = async () => {
    currentPage.value++
    await historyStore.filterHistories()
}

const onDownloadFile = (id: string) => {
    notificationsStore.downloadFile(id)
}

onMounted(() => {
    const translation_uuid = route.query.id
    currentPage.value = 1
    if (translation_uuid) {
        historyStore.fetchHistoryById(translation_uuid)
    } else {
        historyStore.filterHistories()
    }
})

//watch query id
watch(
    () => route.query.id,
    async () => {
        const translation_uuid = route.query.id
        if (translation_uuid) {
            historyStore.fetchHistoryById(translation_uuid)
        } else {
            historyStore.filterHistories()
        }
    }
)
</script>
