<template>
    <a
        class="hidden group md:block z-10 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 inline-flex justify-center w-20 h-20 rounded-full bg-gray-50 border border-gray-200 dark:bg-gray-900 dark:border-gray-600"
    >
        <div
            class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-3 h-[100px] bg-gray-50 dark:bg-gray-900"
        ></div>

        <button
            type="button"
            @click="translateStore.translate(route.name.toString())"
            :disabled="isDisableTranslateButton(route.name)"
            class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-gray-600 w-14 h-14 bg-white hover:bg-primary-800 focus:ring-4 focus:outline-none border border-gray-200 focus:ring-primary-300 font-medium rounded-full text-sm p-1 text-center inline-flex items-center mr-2 dark:bg-primary-600 dark:text-white dark:hover:bg-gray-200 dark:hover:text-primary-600 dark:focus:ring-primary-800 dark:border-gray-600"
            :class="{
                'hover:bg-white hover:dark:bg-primary-600 hover:text-black hover:dark:text-white cursor-not-allowed':
                    isDisableTranslateButton(route.name),
                'hover:bg-primary-800 hover:text-white': !isDisableTranslateButton(route.name),
                'p-3.5': route.name === 'documents' || !isTranslating,
            }"
        >
            <template v-if="isTranslating">
                <svg
                    aria-hidden="true"
                    class="inline w-16 h-16 text-gray-200 animate-spin dark:text-primary-800 fill-primary-600 dark:fill-gray-300"
                    viewBox="0 0 100 101"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path
                        d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                        fill="currentColor"
                    />
                    <path
                        d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                        fill="currentFill"
                    />
                </svg>

                <svg
                    v-if="route.name === 'index'"
                    class="w-4 h-4 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-gray-600 dark:group-hover:text-primary-600"
                    aria-hidden="true"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="currentColor"
                    viewBox="0 0 12 16"
                >
                    <path
                        d="M3 0H2a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h1a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2Zm7 0H9a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h1a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2Z"
                    />
                </svg>
            </template>
            <svg
                v-else
                aria-hidden="true"
                class="w-12 h-12"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
            >
                <path
                    fill-rule="evenodd"
                    d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
                    clip-rule="evenodd"
                ></path>
            </svg>
            <span class="sr-only">Icon description</span>
        </button>
    </a>
</template>

<script setup lang="ts">
import LimitedWarning from '~/components/LimitedWarning.vue'
import { storeToRefs } from 'pinia'
import { useTranslateStore } from '~/stores/translate'
const translateStore = useTranslateStore()
const route = useRoute()
const { isTranslating, inputText, inputFile, showWarningDocument, isDisableTranslateButton } =
    storeToRefs(translateStore)
</script>
