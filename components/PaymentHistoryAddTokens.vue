<template>
    <li class="mb-10 ml-6">
        <span
            class="absolute flex items-center justify-center w-6 h-6 bg-primary-100 rounded-full -left-3 ring-2 ring-white dark:ring-gray-900 dark:bg-primary-900"
            :class="{
                'bg-green-100 dark:bg-green-900': props.isSuccess,
                'bg-red-100 dark:bg-red-900': !props.isSuccess,
            }"
        >
            <svg
                v-if="props.isSuccess"
                class="w-2.5 h-2.5 text-gray-800 dark:text-gray-300"
                aria-hidden="true"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 16 12"
            >
                <path
                    stroke="currentColor"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M1 5.917 5.724 10.5 15 1.5"
                />
            </svg>
            <svg
                v-else
                class="w-2.5 h-2.5 text-gray-800 dark:text-gray-300"
                aria-hidden="true"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 16 16"
            >
                <path
                    stroke="currentColor"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M1 1l14 14M15 1L1 15"
                />
            </svg>
        </span>
        <div class="flex flex-inline items-center justify-between">
            <h3 class="flexmb-1 text-lg font-semibold text-gray-900 dark:text-white">
                {{ $abbreviatedUnit(props.quantity * tokenUnit) }}
                <small class="text-xs">{{ $t('tokens') }}</small>
                <span
                    class="bg-orange-100 text-orange-800 text-sm font-medium mr-2 px-2.5 py-0.5 rounded dark:bg-yellow-900 dark:text-yellow-300 ml-2"
                >
                    {{ $t('Add tokens') }}
                </span>
                <span
                    v-if="props.status && props.isSuccess"
                    class="bg-primary-100 text-primary-800 text-sm font-medium mr-2 px-2.5 py-0.5 rounded dark:bg-primary-900 dark:text-primary-300"
                >
                    {{ $t('Successful') }}
                </span>
                <span
                    v-else
                    class="bg-red-100 text-red-800 text-sm font-medium mr-2 px-2.5 py-0.5 rounded dark:bg-red-900 dark:text-red-300"
                >
                    {{ $t('Failure') }}
                </span>
            </h3>
            <div v-if="props.amount_divide_100 && props.isSuccess" class="text-red-400 text-lg">
                - {{ props.amount_divide_100 }}$
            </div>
            <div v-else class="text-gray-400 text-lg">{{ props.amount_divide_100 }}$</div>
        </div>
        <time class="block mb-2 text-sm font-normal leading-none text-gray-400 dark:text-gray-500">{{
            createdAtRawFormat
        }}</time>
        <p class="mb-1 text-sm font-normal text-gray-500 dark:text-gray-400">{{ $t('Order ID: ') }}{{ props.uuid }}</p>
        <p class="mb-4 text-sm font-normal text-gray-500 dark:text-gray-400">{{ $t('Paypal ID: ') }}{{ props.external_order_id }}</p>
    </li>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { storeToRefs } from 'pinia'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import relativeTime from 'dayjs/plugin/relativeTime'
import { usePaymentsStore } from '~/stores/payments'
const paymentsStore = usePaymentsStore()
const { tokenUnit } = storeToRefs(paymentsStore)

const { $abbreviatedUnit } = useNuxtApp()
dayjs.extend(utc)
dayjs.extend(relativeTime)

const props = defineProps<{
    id: number
    uuid: string
    user_id: string
    product_id: string
    status: string
    amount: number
    amount_divide_100: number
    quantity: number
    type: string
    payment_method: string
    created_at: string
    updated_at: string
    isSuccess?: boolean
    external_order_id: string
}>()

const createdAtRawFormat = computed(() => dayjs.unix(dayjs.utc(props.created_at).unix()).format('YYYY-MM-DD HH:mm:ss'))
</script>
