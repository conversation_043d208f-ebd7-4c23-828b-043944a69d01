<script setup>
import VueWriter from 'vue-writer'

const titleAppArrs = [
    '<PERSON><PERSON> chà<PERSON>, tôi là DoctransGPT',
    '你好，我是DoctransGPT',
    '<PERSON><PERSON>, soy DoctransGPT',
    '<PERSON><PERSON>, ich bin DoctransGPT',
    '<PERSON><PERSON>, je suis DoctransGPT',
    '<PERSON><PERSON>, ik ben DoctransGPT',
    '<PERSON><PERSON><PERSON>, eu sou DoctransGPT',
    'C<PERSON>o, sono DoctransGPT',
    'Hei, jeg er DoctransGPT',
    '<PERSON><PERSON><PERSON><PERSON>, jestem DoctransGPT',
    'Hej, jeg er DoctransGPT',
    'Hei, olen DoctransGPT',
    '<PERSON><PERSON>, én vagyok DoctransGPT',
    '<PERSON><PERSON><PERSON>, eu sou o DoctransGPT',
    'Привет, я DoctransGPT',
    'Γεια σας, είμαι DoctransGPT',
    'שלום, אני DoctransGPT',
    'こんにちは、私はDoctransGPTです',
    '안녕하세요, 저는 DoctransGPT입니다',
    'مرحبًا ، أنا DoctransGPT',
    'สวัสดีค่ะ ฉันคือ DoctransGPT',
    'வணக்கம், நான் DoctransGPT',
    '<PERSON><PERSON><PERSON><PERSON>, ben <PERSON>trans<PERSON>',
    'Привіт, я DoctransGPT',
    'हाय, माझं नाव DoctransGPT आहे',
    'مہربانی کرکے مجھے DoctransGPT کہیں',
    'Hej, jag är DoctransGPT',
    'Hallo, ik ben DoctransGPT',
    'Γεια σας, είμαι DoctransGPT',
    'שלום, אני DoctransGPT',
    'こんにちは、私はDoctransGPTです',
    '안녕하세요, 저는 DoctransGPT입니다',
    'مرحبًا ، أنا DoctransGPT',
    'สวัสดีค่ะ ฉันคือ DoctransGPT',
    'வணக்கம், நான் DoctransGPT',
    'Merhaba, ben DoctransGPT',
    'Привіт, я DoctransGPT',
    'हाय, माझं नाव DoctransGPT आहे',
    'مہربانی کرکے مجھے DoctransGPT کہیں',
    'Hej, jag är DoctransGPT',
    'Hallo, ik ben DoctransGPT',
    'Γεια σας, είμαι DoctransGPT',
    'שלום, אני DoctransGPT',
    'こんにちは、私はDoctransGPTです',
    '안녕하세요, 저는 DoctransGPT입니다',
    'مرحبًا ، أنا DoctransGPT',
    'สวัสดีค่ะ ฉันคือ DoctransGPT',
    'வணக்கம், நான் DoctransGPT',
    'Merhaba, ben DoctransGPT',
    'Привіт, я DoctransGPT',
    'हाय, माझं नाव DoctransGPT आहे',
    'مہربانی کرکے مجھے DoctransGPT کہیں',
]

const props = defineProps({
    dark: {
        type: Boolean,
        default: () => false,
    },
})
</script>

<template>
    <div class="grow-0 mr-3 ml-0">
        <img class="w-12 h-12" src="~/assets/images/logo.png" alt="logo" />
    </div>
    <div class="grow text-2xl font-semibold">
        <p class="text-xs pb-1">{{ $t("Hi, I'm DoctransGPT") }}</p>
        <div>
            <vue-writer
                :array="titleAppArrs"
                :eraseSpeed="30"
                :typeSpeed="100"
                :delay="1000"
                caret="underscore"
                class="overflow-x-clip truncate"
            />
        </div>
    </div>
</template>
