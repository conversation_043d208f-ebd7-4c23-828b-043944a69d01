<template>
    <li class="mb-10 ml-8">
        <span
            class="absolute flex items-center justify-center w-8 h-8 bg-primary-100 rounded-full -left-4 ring-8 ring-gray-50 dark:ring-gray-900 dark:bg-primary-900"
        >
            <svg
                class="w-5 h-5 text-primary-800 dark:text-primary-300"
                aria-hidden="true"
                fill="none"
                stroke="currentColor"
                stroke-width="1.5"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
            >
                <path
                    d="M10.5 21l5.25-11.25L21 21m-9-3h7.5M3 5.621a48.474 48.474 0 016-.371m0 0c1.12 0 2.233.038 3.334.114M9 5.25V3m3.334 2.364C11.176 10.658 7.69 15.08 3 17.502m9.334-12.138c.896.061 1.785.147 2.666.257m-4.589 8.495a18.023 18.023 0 01-3.827-5.802"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                ></path>
            </svg>
        </span>
        <div
            class="px-4 pt-2 pb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-700 dark:border-gray-600"
        >
            <div class="items-center justify-between mb-5 sm:flex">
                <time
                    class="group cursor-pointer flex flex-inline space-x-2 items-center text-xs font-normal text-gray-400 sm:order-last sm:mb-0 justify-between sm:justify-start"
                >
                    <div class="group-hover:hidden">{{ createdAtFormat }}</div>
                    <div class="hidden group-hover:block">{{ createdAtRawFormat }}</div>
                    <a
                        @click="emit('delete')"
                        class="inline-flex -mr-1 justify-center p-2 text-gray-500 rounded-full cursor-pointer dark:text-gray-400 dark:hover:text-white hover:text-gray-900 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer"
                    >
                        <svg
                            class="w-5 h-5"
                            aria-hidden="true"
                            fill="none"
                            stroke="currentColor"
                            stroke-width="1.5"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path d="M6 18L18 6M6 6l12 12" stroke-linecap="round" stroke-linejoin="round"></path>
                        </svg>
                    </a>
                </time>
                <div class="text-sm font-normal text-gray-500 lex dark:text-gray-300">
                    <a class="font-base text-gray-900 dark:text-white mr-2">
                        {{ $t('languages-list.' + props.origin_lang?.toLowerCase()) }} →
                        {{ $t('languages-list.' + props.target_lang?.toLowerCase()) }}
                    </a>
                    <span
                        class="bg-yellow-100 text-gray-800 text-xs font-normal mr-2 px-2.5 py-0.5 rounded dark:bg-yellow-600 dark:text-gray-300"
                    >
                        {{ $t('tokens', { value: used_token }) }}</span
                    >
                    <span
                        class="bg-green-100 text-gray-800 text-xs font-normal mr-2 px-2.5 py-0.5 rounded dark:bg-green-600 dark:text-gray-300"
                    >
                        {{ used_model || 'GPT-3.5' }}</span
                    >
                    <span
                        v-if="props.custom_prompt"
                        class="bg-teal-500 text-gray-100 text-xs font-normal mr-2 px-2.5 py-0.5 rounded dark:bg-teal-300 dark:text-gray-700"
                    >
                        {{ $t('Custom prompt') }}</span
                    >
                </div>
            </div>

            <div class="flex flex-col space-y-3">
                <a
                    @click="toggleIsShowFull"
                    class="cursor-pointer p-3 text-sm font-normal text-gray-500 border border-gray-200 rounded-lg bg-gray-50 dark:bg-gray-600 dark:border-gray-500 dark:text-gray-300"
                >
                    <div :class="isShowFull ? 'break-words' : 'truncate'">{{ props.trans_input }}</div>
                    <div
                        v-if="props.custom_prompt"
                        class="border-t border-gray-200 dark:bg-gray-600 mt-2 pt-2 text-teal-500 dark:text-teal-300"
                    >
                        <div class="font-semibold">{{ $t('Custom prompt') }}:</div>
                        {{ props.custom_prompt || 'Here is your custom prompt' }}
                    </div>
                </a>

                <div class="flex flex-col md:flex-row justify-center">
                    <span
                        v-if="domain"
                        class="bg-pink-100 text-pink-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded dark:bg-pink-900 dark:text-pink-300"
                    >
                        {{ $t('Domain') }}: {{ $t(domain) }}
                    </span>
                    <span
                        v-if="tone"
                        class="bg-indigo-100 text-indigo-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded dark:bg-indigo-900 dark:text-indigo-300"
                        >{{ $t('Tone') }}: {{ $t(tone) }}</span
                    >
                    <span
                        v-if="writing_style"
                        class="bg-purple-100 text-purple-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded dark:bg-purple-900 dark:text-purple-300"
                        >{{ $t('Writing style') }}: {{ $t(writing_style) }}</span
                    >
                </div>
                <div class="flex justify-center text-primary-500 dark:text-primary-300">
                    <svg
                        class="w-5 h-5"
                        aria-hidden="true"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="1.5"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M19.5 13.5L12 21m0 0l-7.5-7.5M12 21V3"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                        ></path>
                    </svg>
                </div>
                <a
                    @click="toggleIsShowFull"
                    class="cursor-pointer p-3 text-sm font-normal text-gray-500 border border-gray-200 rounded-lg bg-gray-50 dark:bg-gray-600 dark:border-gray-500 dark:text-gray-300"
                    :class="isShowFull ? 'break-words' : 'truncate'"
                >
                    {{ props.trans_result }}
                </a>
            </div>
            <div class="shrink-0 mt-4 flex justify-end">
                <HistoryItemRate @onRated="onRated" :uuid="props.uuid" :rating="props.rating" />
            </div>
        </div>
    </li>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import relativeTime from 'dayjs/plugin/relativeTime'
import HistoryItemRate from './HistoryItemRate.vue'

dayjs.extend(utc)
dayjs.extend(relativeTime)

const props = defineProps<{
    uuid: string
    trans_input: string
    trans_result: string
    custom_prompt: string
    created_at: string
    origin_lang: string
    target_lang: string
    used_token: number
    domain: string
    tone: string
    writing_style: string
    rating: string
    used_model: string
}>()
const emit = defineEmits(['delete', 'rated'])

const isShowFull = ref(false)
const toggleIsShowFull = () => {
    isShowFull.value = true
}
const createdAtFormat = computed(() => dayjs().to(dayjs.utc(props.created_at)))
const createdAtRawFormat = computed(() => dayjs.unix(dayjs.utc(props.created_at).unix()).format('YYYY-MM-DD HH:mm:ss'))

const onRated = (rated: any) => {
    emit('rated', { ...rated, uuid: props.uuid })
}
</script>
