<template>
  <div class="space-x-2">
    <button
      v-for="prompt in prompts"
      type="button"
      @click="handlePromptClick(prompt)"
      class="px-3 py-2 text-xs font-medium text-center text-white bg-primary-400 rounded-full hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 dark:bg-primary-500 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
    >
      {{ prompt.text }}
    </button>
  </div>
</template>

<script lang="ts" setup>
import { storeToRefs } from "pinia";
import { useTranslateStore } from "~/stores/translate";
import { useI18n } from 'vue-i18n'
const { t } = useI18n({ useScope: 'global' })
const translateStore = useTranslateStore();
const { customPrompt } = storeToRefs(translateStore);

const prompts = [
  {
    id: 1,
    text: t("Email format"),
    value: t("Translate and write like an email"),
  },
  {
    id: 2,
    text: t("Poem format"),
    value: t("Translate and write like a poem"),
  },
];

const handlePromptClick = (prompt: any) => {
  customPrompt.value = prompt.value;
};
</script>
