<template>
    <div
        id="mega-menu-full-dropdown"
        class="-top-2 pb-6 absolute divide-y divide-gray-100 dark:divide-gray-800 w-full max-w-screen-xl z-10 hidden bg-white border border-gray-200 rounded-md dark:bg-gray-800 dark:border-gray-600"
    >
        <form @submit.prevent="onEnter" class="flex items-center px-10 py-6">
            <div class="relative w-full">
                <input
                    ref="searchInput"
                    @input="(e) => onFilterLangs(e?.target?.value)"
                    autofocus="true"
                    type="text"
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-4 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-500 placeholder-gray-300 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                    :placeholder="$t('Search languages')"
                    :value="filterKeyword"
                />
                <button
                    type="button"
                    class="absolute inset-y-0 right-0 flex items-center pr-3"
                    @click="filterKeyword = ''"
                >
                    <svg
                        class="w-6 h-6 text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
                        aria-hidden="true"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="1.5"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path d="M6 18L18 6M6 6l12 12" stroke-linecap="round" stroke-linejoin="round"></path>
                    </svg>
                </button>
            </div>
        </form>
        <div
            class="min-h-[300px] scrollbar-thin h-[calc(100vh-181px)] md:h-[calc(100vh-250px)] overflow-auto pb-6 md:pb-6"
        >
            <p v-if="languagesListGroups.length == 0" class="text-center pt-8 text-thin text-lg">
                {{ $t('No results') }}
            </p>
            <div id="language-options-select" v-else class="px-6 pb-2 relative">
                <div class="grid grid-cols-1 lg:grid-cols-6 md:grid-cols-4 gap-2 mb-2 md:mb-1">
                    <div
                        v-if="selectLanguageFor === 'from'"
                        @click="changeLanguage('Detect language')"
                        class="hover:bg-gray-100 md:hidden font-bold dark:hover:bg-gray-600 px-6 ml-3 cursor-pointer py-1 text-sm text-black font-bold dark:text-white"
                    >
                        {{ $t('Detect language') }}
                    </div>
                    <div
                        v-for="lang in languagesListGroups"
                        @click="addLanguage(lang.key)"
                        class="flex flex-inline items-center hover:bg-gray-100 dark:hover:bg-gray-600 px-6 ml-3 cursor-pointer py-1 text-sm text-gray-900"
                        :class="
                            [
                                selectLanguageFor === 'from' ? selectedTransFromLang : null,
                                selectLanguageFor === 'to' ? selectedTransToLang : null,
                            ].includes(lang.key)
                                ? 'text-primary-700 font-semibold dark:text-primary-400'
                                : 'font-thin dark:text-white'
                        "
                    >
                        <div>{{ $t(`${lang.name}`) }}</div>
                        <svg
                            v-if="
                                [
                                    selectLanguageFor === 'from' ? selectedTransFromLang : null,
                                    selectLanguageFor === 'to' ? selectedTransToLang : null,
                                ].includes(lang.key)
                            "
                            class="ml-1 w-3 h-3 text-primary-700 dark:text-primary-400"
                            aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                        >
                            <path
                                d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z"
                            />
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import enMessages from '~/locales/en.json'
import _ from 'lodash'
import { Dropdown } from 'flowbite'
import type { DropdownOptions, DropdownInterface } from 'flowbite'
import { storeToRefs } from 'pinia'
import { useTranslateStore } from '~~/stores/translate'
const translateStore = useTranslateStore()

const searchInput = ref(null)
let dropdown: DropdownInterface = null

const { t, locale } = useI18n({ useScope: 'global' })
const {
    openSelectLanguage,
    selectLanguageFor,
    transFromLanguagesList,
    transToLanguagesList,
    selectedTransFromLang,
    selectedTransToLang,
} = storeToRefs(translateStore)

const filterKeyword = ref('')
const languageKeysList = Object.keys(enMessages['languages-list']).filter((s) => s !== 'detect language')
const languageObjectList = languageKeysList.map((s) => ({
    key: s,
    name: t(`languages-list.${s}`),
}))
const languagesListFiltered = ref(languageObjectList)

const languagesListGroups = computed(() => {
    return _(languagesListFiltered.value).sortBy('name').value()
})

const onEnter = () => {
    if (languagesListFiltered.value.length > 0) {
        addLanguage(languagesListFiltered.value[0].key)
    }
}

watch(selectedTransFromLang, (value) => {
    if (value) {
        localStorage.setItem('transFromLang', value)
    }
})

watch(selectedTransToLang, (value) => {
    if (value) {
        localStorage.setItem('transToLang', value)
    }
})

watch(transFromLanguagesList, (value) => {
    if (value) {
        localStorage.setItem('transFromLanguagesList', JSON.stringify(value))
    }
})

watch(transToLanguagesList, (value) => {
    if (value) {
        localStorage.setItem('transToLanguagesList', JSON.stringify(value))
    }
})
watch(filterKeyword, (value) => {
    languagesListFiltered.value = languageObjectList.filter((lang) =>
        lang.name.toLowerCase().includes(value.toLowerCase())
    )
})

const onFilterLangs = (value) => {
    filterKeyword.value = value
    languagesListFiltered.value = languageObjectList.filter((lang) =>
        lang.name.toLowerCase().includes(value.toLowerCase())
    )
}

watch(openSelectLanguage, (value) => {
    if (value) {
        dropdown?.show()
        searchInput.value?.focus()
        filterKeyword.value = ''
    } else {
        dropdown?.hide()
    }
})

const addLanguage = (lang) => {
    translateStore.addTransLang(lang)
}
const changeLanguage = (language: string) => {
    translateStore.selectLanguage(selectLanguageFor.value, language)
}
onMounted(() => {
    const $targetEl = document.getElementById('mega-menu-full-dropdown')
    const $triggerEl: HTMLElement = document.getElementById('mega-menu-full-dropdown-button')
    // options with default values
    const options: DropdownOptions = {
        triggerType: 'click',
        onHide: () => {
            translateStore.setOpenSelectLanguage(false)
        },
        onShow: () => {
            translateStore.setOpenSelectLanguage(true)
        },
    }

    dropdown = new Dropdown($targetEl, $triggerEl, options)
})
</script>

<style>
#mega-menu-full-dropdown {
    transform: translate3d(0px, 48px, 0px) !important;
}
</style>
