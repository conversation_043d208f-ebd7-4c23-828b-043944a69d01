<template>
    <select
        v-model="translateOptions.translateDomain"
        :disabled="isUploading || showCustomPrompt"
        class="col-span-2 md:col-span-1 text-xs font-medium border border-gray-300 dark:border-gray-600 bg-gray-300 dark:bg-gray-600 rounded-lg block w-full p-2.5 focus:ring-primary-500 dark:focus:ring-primary-500 dark:focus:border-primary-500"
        :class="(isUploading || showCustomPrompt) ? 'bg-gray-300 dark:bg-gray-600 text-gray-400 dark:text-gray-400 cursor-not-allowed' : 'text-gray-900 dark:text-white  hover:bg-gray-400 dark:hover:bg-gray-700'"
    >
        <option selected value="">{{ $t('Select domain') }}</option>
        <option v-for="{ value, displayText } in options" :key="value" :value="value">{{ $t(displayText) }}</option>
    </select>
</template>

<script lang="ts" setup>
import _ from 'lodash'
import { useI18n } from 'vue-i18n'
import { useTranslateStore } from '~/stores/translate'
import { storeToRefs } from 'pinia'
const { t, locale } = useI18n({ useScope: 'global' })
const translateStore = useTranslateStore()
const { translateOptions, isUploading, showCustomPrompt } = storeToRefs(translateStore)

const options = _.orderBy(
    [
        { value: 'Business', text: 'Business' },
        { value: 'Legal', text: 'Legal' },
        { value: 'Medical', text: 'Medical' },
        { value: 'Technical', text: 'Technical' },
        { value: 'Financial', text: 'Financial' },
        { value: 'Educational', text: 'Educational' },
        { value: 'Creative', text: 'Creative' },
        { value: 'Social', text: 'Social' },
        { value: 'Scientific', text: 'Scientific' },
        { value: 'Automotive', text: 'Automotive' },
        { value: 'Government', text: 'Government' },
        { value: 'Military', text: 'Military' },
        { value: 'Religious', text: 'Religious' },
        { value: 'Media', text: 'Media' },
        { value: 'Entertainment', text: 'Entertainment' },
        { value: 'Sports', text: 'Sports' },
        { value: 'Travel', text: 'Travel' },
        { value: 'Food & Beverage', text: 'Food & Beverage' },
        { value: 'Retail & Shopping', text: 'Retail & Shopping' },
        { value: 'Home & Garden', text: 'Home & Garden' },
        { value: 'Fashion & Beauty', text: 'Fashion & Beauty' },
        { value: 'Art & Design', text: 'Art & Design' },
        { value: 'Music & Audio', text: 'Music & Audio' },
        { value: 'Lifestyle & Hobbies', text: 'Lifestyle & Hobbies' },
        { value: 'Technology & Computing', text: 'Technology & Computing' },
        { value: 'Photography & Video', text: 'Photography & Video' },
        { value: 'Automation & Robotics', text: 'Automation & Robotics' },
    ].map((item) => {
        return {
            ...item,
            displayText: t(item.text),
        }
    }),
    'displayText'
)
</script>
