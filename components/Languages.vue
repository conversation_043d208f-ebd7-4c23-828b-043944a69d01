<template>
    <!-- <button
        type="button"
        id="dropdownLanguageButton"
        class="inline-flex items-center font-medium justify-center px-4 py-2 text-sm text-gray-900 dark:text-white rounded-lg cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 dark:hover:text-white"
    >
        <img class="w-6 h-6 mr-2 rounded-full" :src="`/assets/images/${currentLocale.code}.png`" />
        <span class="flex flex-inline items-center hidden md:flex">
            {{ currentLocale.name }}
            <svg
                class="w-4 h-4 mx-1.5"
                aria-hidden="true"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
            >
                <path
                    fill-rule="evenodd"
                    d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                    clip-rule="evenodd"
                ></path>
            </svg>
        </span>
    </button> -->
    <button
        id="dropdownLanguageButton"
        class="inline-flex items-center text-sm font-medium text-center text-gray-500 hover:text-gray-900 focus:outline-none dark:hover:text-white dark:text-gray-400"
        type="button"
    >
        <img class="w-6 h-6 rounded-full" :src="`/assets/images/${currentLocale.code}.png`" />
    </button>
    <div
        class="z-40 min-w-[150px] hidden my-4 text-base list-none bg-white divide-y divide-gray-100 rounded-lg shadow dark:bg-gray-700"
        id="dropdownLanguage"
    >
        <ul class="py-2 font-medium" role="none">
            <li v-for="locale in selectableLocales" :key="locale.code" @click="changeLocale(locale.code)">
                <a
                    class="block px-4 pr-6 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-600 dark:hover:text-white cursor-pointer"
                    role="menuitem"
                >
                    <div class="inline-flex items-center">
                        <img class="w-4 h-4 mr-2 rounded-full" :src="`/assets/images/${locale.code}.png`" />

                        {{ locale.name }}
                    </div>
                </a>
            </li>
        </ul>
    </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Dropdown } from 'flowbite'
import Languages from '~/components/Languages.vue'
import { storeToRefs } from 'pinia'
import dayjs from 'dayjs'
import 'dayjs/locale/en'
import 'dayjs/locale/zh'
import 'dayjs/locale/ja'
import 'dayjs/locale/vi'
import { useAppStore } from '~~/stores/app'
const router = useRouter()
const appStore = useAppStore()
const { dropdownLanguage } = storeToRefs(appStore)
const locales = [
    {
        code: 'en',
        name: 'English',
    },
    {
        code: 'zh',
        name: '中文',
    },
    {
        code: 'ja',
        name: '日本語',
    },
    {
        code: 'vi',
        name: 'Tiếng Việt',
    },
]

const currentLocale = ref(locales[0])
const selectableLocales = computed(() => locales.filter((l) => l.code !== currentLocale.value.code))

watch(
    () => currentLocale.value,
    (locale) => {
        dayjs.locale(locale.code)
    },
    { immediate: true }
)

onMounted(() => {
    if (process.client) {
        const locale = window.localStorage.getItem('locale')
        if (locale) {
            currentLocale.value = locales.find((l) => l.code === locale)
            dayjs.locale(locale)
        }

        const $dropdownLangEl = document.getElementById('dropdownLanguage')
        const $triggerDropdownLangEl = document.getElementById('dropdownLanguageButton')

        // options with default values
        const options = {
            placement: 'bottom',
            triggerType: 'click',
            // offsetSkidding: 0,
            // offsetDistance: 10,
            delay: 100,
            onHide: () => {
                console.log('dropdownLanguage has been hidden')
            },
            onShow: () => {
                console.log('dropdownLanguage has been shown')
            },
            onToggle: () => {
                console.log('dropdownLanguage has been toggled')
            },
        }

        dropdownLanguage.value = new Dropdown($dropdownLangEl, $triggerDropdownLangEl, options)
    }
})
const { locale } = useI18n({ useScope: 'global' })
const changeLocale = (code) => {
    currentLocale.value = locales.find((l) => l.code === code)
    window.localStorage.setItem('locale', code)
    locale.value = code
    dropdownLanguage.value?.hide()
    window.location.reload()
}
</script>
