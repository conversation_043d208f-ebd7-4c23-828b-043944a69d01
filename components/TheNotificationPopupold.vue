<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { useAppStore } from '~/stores/app'
import { useAuthStore } from '~/stores/auth'
import { onMounted, watch } from 'vue'
import { Modal } from 'flowbite'
import type { ModalOptions, ModalInterface } from 'flowbite'
import dayjs from 'dayjs'
const appStore = useAppStore()
const authStore = useAuthStore()
const { showNotificationPopup } = storeToRefs(appStore)
const { user } = storeToRefs(authStore)
const config = useRuntimeConfig()
let modal: ModalInterface
onMounted(() => {
    if (process.client) {
        const $modalElement: HTMLElement = document.querySelector('#modalNotificationPopup')
        const modalOptions: ModalOptions = {
            placement: 'center-center',
            backdrop: 'dynamic',
            backdropClasses: 'bg-gray-900 bg-opacity-50 dark:bg-opacity-80 fixed inset-0 z-40',
            closable: false,
            onHide: () => {},
            onShow: () => {},
        }

        modal = new Modal($modalElement, modalOptions)

        if (showNotificationPopup.value && user.value) modal.show()
    }
})

const checkLastSeenNotificationPopupWithin8Hours = () => {
    const lastSeenNotificationPopup = localStorage.getItem('lastSeenNotificationPopup')
    if (lastSeenNotificationPopup) {
        const diff = dayjs().diff(dayjs(lastSeenNotificationPopup), 'hour')
        return diff < 8
    }
    return false
}

watch(user, (value) => {
    // const seenNotificationPopup = localStorage.getItem('seenNotificationPopup') === 'true'
    //check user seen notification popup within 8 hours
    const seenNotificationPopup = checkLastSeenNotificationPopupWithin8Hours()
    if (value && value.current_plan === 'FP0001' && config.public.NUXT_SHOW_NOTIFICATION && !seenNotificationPopup)
        showNotificationPopup.value = true
})

watch(showNotificationPopup, (value) => {
    const seenNotificationPopup = checkLastSeenNotificationPopupWithin8Hours()
    if (value && !seenNotificationPopup) {
        modal.show()
    } else {
        modal.hide()
        localStorage.setItem('lastSeenNotificationPopup', dayjs().toISOString())
    }
})

const navigationToPage = (path: string) => {
    navigateTo(path)
    showNotificationPopup.value = false
}
</script>
<template>
    <!-- Main modal -->
    <div
        id="modalNotificationPopup"
        tabindex="-1"
        aria-hidden="true"
        class="scrollbar-thin fixed top-0 left-0 right-0 z-50 hidden w-full p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full"
    >
        <div class="relative w-full max-w-xl max-h-full">
            <!-- Modal content -->
            <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                <!-- Modal header -->
                <div class="flex items-start justify-between p-4 border-b rounded-t dark:border-gray-600">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                        {{ $t('Upgrade Your Translation Experience with GPT-4!') }}
                    </h3>
                    <button
                        @click="
                            () => {
                                showNotificationPopup = false
                            }
                        "
                        type="button"
                        class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm p-1.5 ml-auto inline-flex items-center dark:hover:bg-gray-600 dark:hover:text-white"
                    >
                        <svg
                            aria-hidden="true"
                            class="w-5 h-5"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                fill-rule="evenodd"
                                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                clip-rule="evenodd"
                            ></path>
                        </svg>
                        <span class="sr-only">Close modal</span>
                    </button>
                </div>
                <!-- Modal body -->
                <div>
                    <section class="bg-white dark:bg-gray-700 dark:text-gray-300 text-sm py-4">
                        <div class="p-4 flex flex-col">
                            <p class="mb-2">
                                {{ $t('Dear users,') }}
                            </p>
                            <p>
                                {{
                                    $t(
                                        'Exciting news! GPT-4 and GPT-4 Turbo are now available for our premium users. Unlock advanced translation capabilities and experience faster performance than ever before! Upgrade now for an enhanced translation journey.'
                                    )
                                }}
                            </p>
                            <div class="flex items-center space-x-1 mt-4 mb-1">
                                <svg
                                    class="w-5 h-5 text-yellow-400 dark:text-yellow-300"
                                    aria-hidden="true"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 21 20"
                                >
                                    <path
                                        stroke="currentColor"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="m8.806 5.614-4.251.362-2.244 2.243a1.058 1.058 0 0 0 .6 1.8l3.036.356m9.439 1.819-.362 4.25-2.243 2.245a1.059 1.059 0 0 1-1.795-.6l-.449-2.983m9.187-12.57a1.536 1.536 0 0 0-1.26-1.26c-1.818-.313-5.52-.7-7.179.96-1.88 1.88-5.863 9.016-7.1 11.275a1.05 1.05 0 0 0 .183 1.25l.932.939.937.936a1.049 1.049 0 0 0 1.25.183c2.259-1.24 9.394-5.222 11.275-7.1 1.66-1.663 1.275-5.365.962-7.183Zm-3.332 4.187a2.115 2.115 0 1 1-4.23 0 2.115 2.115 0 0 1 4.23 0Z"
                                    />
                                </svg>
                                <div class="text-md text-yellow-400 dark:text-yellow-300">
                                    {{ $t('Upgrade Now!') }}
                                </div>
                            </div>
                            <div class="my-2 flex flex-inline space-x-2">
                                <button
                                    @click="navigationToPage('/pricing-plans')"
                                    type="button"
                                    class="text-white bg-gradient-to-r from-primary-500 via-primary-600 to-primary-700 hover:bg-gradient-to-br focus:ring-4 focus:outline-none focus:ring-primary-300 dark:focus:ring-primary-800 shadow-lg shadow-primary-500/50 dark:shadow-lg dark:shadow-primary-800/80 font-medium rounded-lg text-sm px-3 py-1.5 text-center me-2 mb-2"
                                >
                                    {{ $t('View Service Plans') }}
                                </button>
                                <button
                                    @click="navigationToPage('/profile/add-tokens')"
                                    type="button"
                                    class="text-white bg-gradient-to-r from-primary-500 via-primary-600 to-primary-700 hover:bg-gradient-to-br focus:ring-4 focus:outline-none focus:ring-primary-300 dark:focus:ring-primary-800 shadow-lg shadow-primary-500/50 dark:shadow-lg dark:shadow-primary-800/80 font-medium rounded-lg text-sm px-3 py-1.5 text-center me-2 mb-2"
                                >
                                    {{ $t('Buy Tokens') }}
                                </button>
                            </div>
                            <p>
                                {{
                                    $t(
                                        'We believe in providing convenience and quality that will exceed your expectations!'
                                    )
                                }}
                            </p>
                            <p class="mt-3">
                                {{ $t('Thank you for choosing our services!') }}
                            </p>
                        </div>
                    </section>
                </div>
            </div>
        </div>
    </div>
</template>
