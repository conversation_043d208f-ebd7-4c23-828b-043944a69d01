<template>
    <header>
        <nav
            class="w-full z-20 bg-gray-50 border-gray-200 dark:bg-gray-900 md:border-b md:border-gray-200 dark:border-gray-600"
        >
            <div class="max-w-screen-xl flex flex-wrap items-center justify-between mx-auto p-4">
                <div class="flex items-center">
                    <a
                        id="drawer-menu"
                        class="inline-flex justify-center p-3 text-gray-500 rounded-full cursor-pointer dark:text-gray-400 dark:hover:text-white hover:text-gray-900 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer"
                        @click="appStore.setShowAppDrawer(true)"
                    >
                        <svg
                            class="w-5 h-5 text-gray-500 dark:text-white"
                            aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="currentColor"
                            viewBox="0 0 17 14"
                        >
                            <path
                                d="M16 2H1a1 1 0 0 1 0-2h15a1 1 0 1 1 0 2Zm0 6H1a1 1 0 0 1 0-2h15a1 1 0 1 1 0 2Zm0 6H1a1 1 0 0 1 0-2h15a1 1 0 0 1 0 2Z"
                            />
                        </svg>
                    </a>
                    <a href="/" class="flex items-center cursor-pointer">
                        <img src="~/assets/images/logo.png" class="h-8 mr-3" />
                        <span class="self-center text-base md:text-2xl font-base whitespace-nowrap dark:text-white"
                            >DocTransGPT</span
                        >
                    </a>
                </div>
                <div class="flex flex-inline items-center md:order-2 space-x-3">
                    <div class="flex flex-inline items-center pt-2 mr-2">
                        <div id="notification-icon" v-show="user" class="mr-3.5">
                            <NotificationBar />
                        </div>
                        <div id="website-language-select">
                            <Languages />
                        </div>
                        <!-- <div>
                            <DarkModeToggle />
                        </div> -->
                    </div>
                    <div v-if="isLoggingIn" role="status">
                        <svg
                            aria-hidden="true"
                            class="inline w-7 h-7 text-gray-200 animate-spin dark:text-gray-600 fill-primary-600 dark:fill-primary-300"
                            viewBox="0 0 100 101"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                fill="currentColor"
                            />
                            <path
                                d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                fill="currentFill"
                            />
                        </svg>
                        <span class="sr-only">Loading...</span>
                    </div>
                    <template v-else>
                        <div v-if="user">
                            <div
                                id="userDropdownButton"
                                data-dropdown-toggle="userDropdown"
                                data-dropdown-placement="bottom-start"
                                data-dropdown-offset-distance="50"
                                data-dropdown-offset-skidding="0"
                                class="hidden lg:flex cursor-pointer relative inline-flex items-center justify-center w-10 h-10 overflow-hidden bg-primary-100 rounded-full dark:bg-primary-600"
                            >
                                <img
                                    v-if="user.avatar"
                                    class="w-9 h-9 rounded-full"
                                    :src="user.avatar"
                                    alt="Rounded avatar"
                                />
                                <span v-else class="font-medium text-gray-600 dark:text-gray-300 uppercase">
                                    {{ user.full_name.substring(0, 2) }}
                                </span>
                            </div>
                            <div
                                @click="appStore.setShowUserDrawer(true)"
                                class="lg:hidden cursor-pointer relative inline-flex items-center justify-center w-10 h-10 overflow-hidden bg-primary-100 rounded-full dark:bg-primary-600"
                            >
                                <img
                                    v-if="user.avatar"
                                    class="w-10 h-10 rounded-full"
                                    :src="user.avatar"
                                    alt="Rounded avatar"
                                />
                                <span v-else class="font-medium text-gray-600 dark:text-gray-300 uppercase">
                                    {{ user.full_name.substring(0, 2) }}
                                </span>
                            </div>
                            <!-- Dropdown menu -->
                            <div
                                id="userDropdown"
                                class="absolute z-30 p-2 hidden bg-white divide-y divide-gray-100 rounded-lg shadow-xl border border-gray-200 dark:border-gray-500 dark:bg-gray-700 dark:divide-gray-600"
                            >
                                <div class="p-4">
                                    <ProfileCard />
                                </div>
                                <!-- <div class="px-4 py-3 text-sm text-gray-900 dark:text-white truncate">
                                    <div class="font-bold truncate">
                                        {{ user.full_name }}
                                    </div>
                                    <div class="font-medium truncate">
                                        {{ user.email }}
                                    </div>
                                    <div
                                        data-tooltip-target="tooltip-tokens"
                                        id="tokens_abbreviated"
                                        class="font-medium truncate"
                                    >
                                        {{ $t('tokens', { value: $abbreviatedUnit(user?.tokens) }) }}
                                    </div>
                                    <div
                                        id="tooltip-tokens"
                                        role="tooltip"
                                        class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-white transition-opacity duration-300 bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip dark:bg-gray-700"
                                    >
                                        {{ user.tokens }}
                                        <div class="tooltip-arrow" data-popper-arrow></div>
                                    </div>
                                </div>
                                <ul
                                    class="py-2 text-sm text-gray-700 dark:text-gray-200"
                                    aria-labelledby="avatarButton"
                                >
                                    <li>
                                        <a
                                            @click="router.push({ path: '/profile' })"
                                            class="cursor-pointer block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white"
                                        >
                                            {{ $t('Profile') }}
                                        </a>
                                    </li>
                                </ul>
                                <div class="py-1">
                                    <a
                                        @click="authStore.logout"
                                        class="cursor-pointer block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 dark:text-gray-200 dark:hover:text-white"
                                        >{{ $t('Sign out') }}</a
                                    >
                                </div> -->
                            </div>
                        </div>

                        <button
                            v-else
                            @click="router.push({ path: '/signin' })"
                            type="button"
                            class="hidden md:flex text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
                        >
                            <span class="hidden sm:block"> {{ $t('Sign In') }}</span>

                            <svg
                                aria-hidden="true"
                                class="w-5 h-5 sm:ml-2 sm:-mr-1"
                                fill="none"
                                stroke="currentColor"
                                stroke-width="1.5"
                                viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    d="M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                ></path>
                            </svg>
                        </button>
                    </template>
                </div>
                <div class="items-center justify-between hidden w-full lg:flex md:w-auto md:order-1">
                    <ul
                        id="nav-menu"
                        class="flex flex-col ipad:hidden font-medium p-4 md:p-0 mt-0 border border-gray-100 rounded-lg bg-gray-50 md:flex-row md:space-x-2 md:mt-0 md:border-0 dark:bg-gray-800 md:dark:bg-gray-900 dark:border-gray-700"
                    >
                        <li
                            id="nav-item-translate-text"
                            class="px-2 py-2 text-sm"
                            :class="{
                                'border-b-2 border-primary-500 bg-primary-50 dark:bg-gray-800': route.name == 'index',
                            }"
                        >
                            <a
                                @click="router.push({ path: '/' })"
                                class="flex flex-inline items-center py-2 pl-3 pr-4 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:hover:text-primary-700 md:p-0 dark:text-white md:dark:hover:text-primary-500 dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent dark:border-gray-700 cursor-pointer"
                                :class="{ 'text-primary-700 dark:text-gray-50': route.name == 'index' }"
                            >
                                <svg
                                    class="w-5 h-5 mr-2 group-hover:text-gray-500 dark:text-gray-50 dark:group-hover:text-gray-300"
                                    aria-hidden="true"
                                    fill="none"
                                    stroke="currentColor"
                                    stroke-width="1.5"
                                    viewBox="0 0 24 24"
                                    xmlns="http://www.w3.org/2000/svg"
                                >
                                    <path
                                        d="M10.5 21l5.25-11.25L21 21m-9-3h7.5M3 5.621a48.474 48.474 0 016-.371m0 0c1.12 0 2.233.038 3.334.114M9 5.25V3m3.334 2.364C11.176 10.658 7.69 15.08 3 17.502m9.334-12.138c.896.061 1.785.147 2.666.257m-4.589 8.495a18.023 18.023 0 01-3.827-5.802"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                    ></path>
                                </svg>

                                {{ $t('Text') }}
                            </a>
                        </li>
                        <li
                            id="nav-item-translate-document"
                            class="px-2 py-2 text-sm"
                            :class="{
                                'border-b-2 border-primary-500 bg-primary-50 dark:bg-gray-800':
                                    route.name === 'documents',
                            }"
                        >
                            <a
                                @click="router.push({ path: '/documents' })"
                                class="flex flex-inline items-center py-2 pl-3 pr-4 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:hover:text-primary-700 md:p-0 dark:text-white md:dark:hover:text-primary-500 dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent dark:border-gray-700 cursor-pointer"
                                :class="{ 'text-primary-700 dark:text-gray-50': route.name === 'documents' }"
                            >
                                <svg
                                    class="w-5 h-5 mr-2 group-hover:text-gray-500 dark:text-gray-50 dark:group-hover:text-gray-300"
                                    aria-hidden="true"
                                    fill="none"
                                    stroke="currentColor"
                                    stroke-width="1.5"
                                    viewBox="0 0 24 24"
                                    xmlns="http://www.w3.org/2000/svg"
                                >
                                    <path
                                        d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                    ></path>
                                </svg>
                                {{ $t('Documents') }}
                            </a>
                        </li>
                        <li
                            id="nav-item-translation-history"
                            class="px-2 py-2 text-sm"
                            :class="{
                                'border-b-2 border-primary-500 bg-primary-50 dark:bg-gray-800':
                                    route.name === 'history',
                            }"
                        >
                            <a
                                @click="router.push({ path: '/history' })"
                                class="flex flex-inline items-center py-2 pl-3 pr-4 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:hover:text-primary-700 md:p-0 dark:text-white md:dark:hover:text-primary-500 dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent dark:border-gray-700 cursor-pointer"
                                :class="{ 'text-primary-700 dark:text-gray-50': route.name === 'history' }"
                            >
                                <svg
                                    class="w-5 h-5 mr-2 group-hover:text-gray-500 dark:text-gray-50 dark:group-hover:text-gray-300"
                                    aria-hidden="true"
                                    fill="none"
                                    stroke="currentColor"
                                    stroke-width="1.5"
                                    viewBox="0 0 24 24"
                                    xmlns="http://www.w3.org/2000/svg"
                                >
                                    <path
                                        d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                    ></path>
                                </svg>
                                {{ $t('History') }}
                            </a>
                        </li>
                        <li id="nav-item-translation-history" class="relative px-2 py-2 text-sm">
                            <a
                                href="https://ttsopenai.com/"
                                target="_blank"
                                class="flex flex-inline items-center py-2 pl-1 pr-4 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:hover:text-primary-700 md:p-0 dark:text-white md:dark:hover:text-primary-500 dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent dark:border-gray-700 cursor-pointer"
                            >
                                <Icon icon="iconoir:voice" class="text-xl mr-2" />
                                {{ $t('Vocalize') }}
                                <Icon icon="memory:arrow-top-right" class="text-xs absolute top-2 -right-1" />
                            </a>
                        </li>
                        <li id="nav-item-translation-history" class="relative px-1 py-2 text-sm geminigen">
                            <a
                                href="https://geminigen.ai"
                                target="_blank"
                                class="flex flex-inline items-center py-2 pl-3 pr-4 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:hover:text-primary-700 md:p-0 dark:text-white md:dark:hover:text-primary-500 dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent dark:border-gray-700 cursor-pointer"
                            >
                                <Icon icon="mingcute:ai-line" class="text-xl mr-2" />
                                {{ $t('Image/Video') }}
                                <Icon icon="memory:arrow-top-right" class="text-xs absolute top-2 -right-1.5" />
                            </a>
                        </li>
                    </ul>
                    <div class="md:px-4 md:ml-4 border-l h-full dark:border-gray-600 ipad:border-0">
                        <VersionMenu />
                    </div>
                </div>
            </div>
        </nav>
    </header>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useI18n } from 'vue-i18n'
const { $abbreviatedUnit } = useNuxtApp()
import { Dropdown } from 'flowbite'
import { Tooltip } from 'flowbite'
import Languages from '~/components/Languages.vue'
import ProfileCard from '~/components/ProfileCard.vue'
import NotificationBar from '~/components/NotificationBar.vue'
import { storeToRefs } from 'pinia'
import { useTranslateStore } from '~~/stores/translate'
import { useAuthStore } from '~~/stores/auth'
import { useAppStore } from '~~/stores/app'
import { Icon } from '@iconify/vue'
const translateStore = useTranslateStore()
const authStore = useAuthStore()
const appStore = useAppStore()
const { mode } = storeToRefs(translateStore)
const { isLoggingIn, user, isLoggedIn } = storeToRefs(authStore)
console.log('🚀 ~ file: TheHeader.vue:288 ~ user:', user)
const router = useRouter()
const route = useRoute()

let dropdown
let tooltip
onMounted(async () => {
    await authStore.fetchUserInfo()

    const $dropdownUserEl = document.getElementById('userDropdown')
    const $triggerDropdownUserEl = document.getElementById('userDropdownButton')

    // options with default values
    const options = {
        placement: 'bottom',
        triggerType: 'click',
        offsetSkidding: -120,
    }

    dropdown = new Dropdown($dropdownUserEl, $triggerDropdownUserEl, options)

    const $targetEl = document.getElementById('tooltip-tokens')
    const $triggerEl = document.getElementById('tokens_abbreviated')
    const toolTipOptions = {
        placement: 'left',
        triggerType: 'hover|click',
    }
    tooltip = new Tooltip($targetEl, $triggerEl, toolTipOptions)
})
</script>

<style scoped>
.geminigen {
    background: linear-gradient(135deg, #f97316, #3b82f6);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    position: relative;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.geminigen::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #f97316, #3b82f6);
    border-radius: 8px;
    opacity: 0.1;
    z-index: -1;
    transition: opacity 0.3s ease;
}

.geminigen:hover::before {
    opacity: 0.2;
}

.geminigen:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(249, 115, 22, 0.3);
}

/* Dark mode support */
.dark .geminigen {
    background: linear-gradient(135deg, #fb923c, #60a5fa);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.dark .geminigen::before {
    background: linear-gradient(135deg, #fb923c, #60a5fa);
}
</style>
