<script lang="ts" setup>
import { useAppStore } from '~/stores/app'
import { storeToRefs } from 'pinia'
const appStore = useAppStore()
const { contactEmail } = storeToRefs(appStore)

const config = useRuntimeConfig()
const appVersion = config.public.NUXT_APP_VERSION
</script>

<template>
    <footer class="shadow dark:bg-gray-900">
        <div class="w-full max-w-screen-xl mx-auto pb-4 pt-4 md:pb-4 flex flex-inline justify-between px-4 lg:px-0">
            <span class="block text-sm text-gray-500 text-center dark:text-gray-400">
                © Copyright 2023, DoctransGPT. <a class="text-primary-900 cursor-pointer">Version {{ appVersion }}</a>
            </span>
            <div class="block text-sm text-gray-500 text-center dark:text-gray-400">
                <i18n-t keypath="Contact us" tag="p">
                    <a
                        class="font-semibold underline cursor-pointer"
                        :href="'mailto:' + contactEmail"
                        :class="'text-primary-500 dark:text-primary-500'"
                        >{{ contactEmail }}</a
                    >
                </i18n-t>
            </div>
        </div>
    </footer>
</template>

<style scoped></style>
