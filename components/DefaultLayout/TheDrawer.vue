<template>
  <!-- drawer component -->
  <div
    id="drawer-disable-body-scrolling"
    class="fixed top-0 left-0 z-40 h-screen p-4 overflow-y-auto transition-transform -translate-x-full bg-white w-80 dark:bg-gray-800"
    tabindex="-1"
    aria-labelledby="drawer-disable-body-scrolling-label"
  >
    <a href="/" class="flex items-center cursor-pointer">
      <img src="~/assets/images/logo.png" class="h-8 mr-3" />
      <span class="self-center text-2xl font-base whitespace-nowrap dark:text-white"
        >DocTransGPT</span
      >
    </a>
    <button
      type="button"
      @click="appStore.setShowAppDrawer(false)"
      class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-black rounded-lg text-sm p-1.5 absolute top-2.5 right-2.5 inline-flex items-center dark:hover:bg-gray-600 dark:hover:text-white"
    >
      <svg
        aria-hidden="true"
        class="w-5 h-5"
        fill="currentColor"
        viewBox="0 0 20 20"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          fill-rule="evenodd"
          d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
          clip-rule="evenodd"
        ></path>
      </svg>
      <span class="sr-only">Close menu</span>
    </button>
    <div class="py-4 overflow-y-auto">
      <ul id="drawer-menu-list" class="space-y-2 font-base">
        <li>
          <a
            @click="onPush('/')"
            class="flex items-center p-2 text-black rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
          >
            <svg
              class="w-5 h-5 mr-2 group-hover:text-black dark:text-gray-50 dark:group-hover:text-gray-300"
              aria-hidden="true"
              fill="none"
              stroke="currentColor"
              stroke-width="1.5"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M10.5 21l5.25-11.25L21 21m-9-3h7.5M3 5.621a48.474 48.474 0 016-.371m0 0c1.12 0 2.233.038 3.334.114M9 5.25V3m3.334 2.364C11.176 10.658 7.69 15.08 3 17.502m9.334-12.138c.896.061 1.785.147 2.666.257m-4.589 8.495a18.023 18.023 0 01-3.827-5.802"
                stroke-linecap="round"
                stroke-linejoin="round"
              ></path>
            </svg>
            <span class="ml-3">{{ $t("Text") }}</span>
          </a>
        </li>
        <li>
          <a
            @click="onPush('/documents')"
            class="flex items-center p-2 text-black rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
          >
            <svg
              class="w-5 h-5 mr-2 group-hover:text-black dark:text-gray-50 dark:group-hover:text-gray-300"
              aria-hidden="true"
              fill="none"
              stroke="currentColor"
              stroke-width="1.5"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z"
                stroke-linecap="round"
                stroke-linejoin="round"
              ></path>
            </svg>
            <span class="ml-3">{{ $t("Documents") }}</span>
          </a>
        </li>

        <li>
          <a
            @click="onPush('/history')"
            class="flex items-center p-2 text-black rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
          >
            <svg
              class="w-5 h-5 mr-2 group-hover:text-black dark:text-gray-50 dark:group-hover:text-gray-300"
              aria-hidden="true"
              fill="none"
              stroke="currentColor"
              stroke-width="1.5"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z"
                stroke-linecap="round"
                stroke-linejoin="round"
              ></path>
            </svg>
            <span class="ml-3">{{ $t("History") }}</span>
          </a>
        </li>
        <li>
          <a
            href="https://ttsopenai.com/"
            target="_blank"
            class="flex items-center p-2 text-black rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
          >
            <Icon icon="iconoir:voice" class="text-xl mr-2" />
            <span class="ml-3 relative"
              >{{ $t("Vocalize") }}

              <Icon
                icon="memory:arrow-top-right"
                class="text-xs absolute top-0.5 -right-3"
              />
            </span>
          </a>
        </li>
        <li>
          <a
            href="https://geminigen.ai"
            target="_blank"
            class="flex items-center p-2 text-black rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
          >
            <Icon icon="mingcute:ai-line" class="text-xl mr-2" />
            <span class="ml-3 relative"
              >{{ $t("Image/Video") }}

              <Icon
                icon="memory:arrow-top-right"
                class="text-xs absolute top-0.5 -right-3"
              />
            </span>
          </a>
        </li>
        <li>
          <a
            @click="onPush('/pricing-plans')"
            class="flex items-center p-2 text-black rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
          >
            <svg
              class="w-5 h-5 r-2 group-hover:text-black dark:text-gray-50 dark:group-hover:text-gray-300"
              aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 20 18"
            >
              <path
                stroke="currentColor"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="1.3"
                d="M5 2a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v10a1 1 0 0 1-1 1M1 9h14M2 5h12a1 1 0 0 1 1 1v10a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1V6a1 1 0 0 1 1-1Z"
              />
            </svg>
            <span class="ml-5">{{ $t("Pricing Plans") }}</span>
          </a>
        </li>
        <li>
          <a
            @click="onPush('/privacy')"
            class="flex items-center p-2 text-black rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
          >
            <svg
              class="w-5 h-5 mr-2 group-hover:text-black dark:text-gray-50 dark:group-hover:text-gray-300"
              aria-hidden="true"
              fill="none"
              stroke="currentColor"
              stroke-width="1.5"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                stroke="currentColor"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="1.5"
                d="M8.783 19h-6.85A.97.97 0 0 1 1 18V5.828a2 2 0 0 1 .586-1.414l2.828-2.828A2 2 0 0 1 5.828 1h8.239A.97.97 0 0 1 15 2v2.478M6 1v4a1 1 0 0 1-1 1H1m13.056 13a11.337 11.337 0 0 1-4.889-9.167L14.056 8l4.888 1.833A11.336 11.336 0 0 1 14.056 19Z"
              />
            </svg>
            <span class="ml-3">{{ $t("Privacy policy") }}</span>
          </a>
        </li>
        <li>
          <a
            @click="onPush('/terms')"
            class="flex items-center p-2 text-black rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
          >
            <svg
              class="w-5 h-5 mr-2 group-hover:text-black dark:text-gray-50 dark:group-hover:text-gray-300"
              aria-hidden="true"
              fill="none"
              stroke="currentColor"
              stroke-width="1.5"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                stroke="currentColor"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="1.5"
                d="M6 1v4a1 1 0 0 1-1 1H1m4 6 2 2 4-4m4-8v16a.97.97 0 0 1-.933 1H1.933A.97.97 0 0 1 1 18V5.828a2 2 0 0 1 .586-1.414l2.828-2.828A2 2 0 0 1 5.828 1h8.239A.97.97 0 0 1 15 2Z"
              />
            </svg>
            <span class="ml-3">{{ $t("Terms of service") }}</span>
          </a>
        </li>
        <li>
          <a
            @click="onPush('/faq')"
            class="flex items-center p-2 text-black rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
          >
            <svg
              class="w-5 h-5 mr-2 group-hover:text-black dark:text-gray-50 dark:group-hover:text-gray-300"
              aria-hidden="true"
              fill="none"
              stroke="currentColor"
              stroke-width="1.5"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                stroke="currentColor"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="1.5"
                d="M7.529 7.988a2.502 2.502 0 0 1 5 .191A2.441 2.441 0 0 1 10 10.582V12m-.01 3.008H10M19 10a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
              />
            </svg>
            <span class="ml-3">{{ $t("FAQ") }}</span>
          </a>
        </li>
        <li>
          <a
            class="flex cursor-pointer items-center p-2 text-black rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
          >
            <svg
              class="w-5 h-5 mr-2 group-hover:text-black dark:text-gray-50 dark:group-hover:text-gray-300"
              aria-hidden="true"
              fill="none"
              stroke="currentColor"
              stroke-width="1.5"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M21.752 15.002A9.718 9.718 0 0118 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 003 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 009.002-5.998z"
                stroke-linecap="round"
                stroke-linejoin="round"
              ></path>
            </svg>
            <span class="flex-1 ml-3 whitespace-nowrap">{{ $t("Dark mode") }}</span>
            <span class="inline-flex items-center">
              <label class="relative inline-flex items-center cursor-pointer">
                <input type="checkbox" value="" class="sr-only peer" v-model="darkMode" />
                <div
                  class="w-11 h-6 bg-gray-200 rounded-full peer dark:bg-gray-700 peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"
                ></div>
              </label>
            </span>
          </a>
        </li>
        <li>
          <a
            class="flex flex-inline items-center cursor-pointer p-2 text-black rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
          >
            <svg
              class="w-5 h-5 mr-2 group-hover:text-black dark:text-gray-50 dark:group-hover:text-gray-300"
              aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 22 21"
            >
              <path
                stroke="currentColor"
                stroke-linecap="round"
                stroke-width="1.5"
                d="M7.24 7.194a24.16 24.16 0 0 1 3.72-3.062m0 0c3.443-2.277 6.732-2.969 8.24-1.46 2.054 2.053.03 7.407-4.522 11.959-4.552 4.551-9.906 6.576-11.96 4.522C1.223 17.658 1.89 14.412 4.121 11m6.838-6.868c-3.443-2.277-6.732-2.969-8.24-1.46-2.054 2.053-.03 7.407 4.522 11.959m3.718-10.499a24.16 24.16 0 0 1 3.719 3.062M17.798 11c2.23 3.412 2.898 6.658 1.402 8.153-1.502 1.503-4.771.822-8.2-1.433m1-6.808a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z"
              />
            </svg>
            <span class="flex-1 ml-3 whitespace-nowrap">{{ $t("AI Model") }}</span>
            <span class="inline-flex items-center">
              <select
                id="small"
                v-model="chatGPTVersion"
                class="block w-full px-2 text-sm text-black border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
              >
                <option value="gpt-4o-mini">GPT-4o-Mini</option>
                <option
                  value="gpt-4"
                  :disabled="!isGPT4Enabled || !usableModelList.includes('gpt-4')"
                >
                  GPT-4
                </option>
                <option
                  value="gpt-4o"
                  :disabled="!isGPT4Enabled || !usableModelList.includes('gpt-4o')"
                >
                  GPT-4o
                </option>
              </select>
            </span>
          </a>
        </li>
        <li>
          <a
            href="/tour-guide"
            class="flex items-center p-2 text-black rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
          >
            <svg
              class="w-5 h-5 mr-2 group-hover:text-black dark:text-gray-50 dark:group-hover:text-gray-300"
              aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 16 20"
            >
              <path
                stroke="currentColor"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="1.5"
                d="M1 17V2a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H3a2 2 0 0 0-2 2Zm0 0a2 2 0 0 0 2 2h12M5 15V1m8 18v-4"
              />
            </svg>
            <span class="ml-3">{{ $t("How to use") }}</span>
          </a>
        </li>
      </ul>
    </div>
    <div class="mt-6 text-sm font-base w-full text-center text-black dark:text-white">
      <i18n-t keypath="Contact us" tag="p">
        <a
          class="font-semibold underline cursor-pointer"
          :href="'mailto:' + contactEmail"
          :class="'text-primary-500 dark:text-primary-500'"
          >{{ contactEmail }}</a
        >
      </i18n-t>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { Drawer } from "flowbite";
import { onMounted } from "vue";
import { useAppStore } from "~/stores/app";
import { storeToRefs } from "pinia";
import { usePaymentsStore } from "~~/stores/payments";
import { Icon } from "@iconify/vue";
const paymentStore = usePaymentsStore();

const { usableModelList } = storeToRefs(paymentStore);
const config = useRuntimeConfig();
const isGPT4Enabled = config.public.NUXT_MODEL_GPT_4;

const appStore = useAppStore();
const router = useRouter();

const { showAppDrawer, darkMode, contactEmail, chatGPTVersion } = storeToRefs(appStore);
watch(
  () => showAppDrawer.value,
  (value) => {
    if (value) {
      drawer.show();
    } else {
      drawer.hide();
    }
  }
);

const onPush = (path) => {
  router.push(path);
  appStore.setShowAppDrawer(false);
};

let drawer = null;
const options: DrawerOptions = {
  placement: "left",
  backdrop: true,
  bodyScrolling: false,
  edge: false,
  edgeOffset: "",
  backdropClasses: "bg-gray-900 bg-opacity-50 dark:bg-opacity-80 fixed inset-0 z-30",
  onHide: () => {
    appStore.setShowAppDrawer(false);
  },
  onShow: () => {
    appStore.setShowAppDrawer(true);
  },
};

onMounted(() => {
  if (process.client) {
    const $targetEl = document.getElementById("drawer-disable-body-scrolling");
    drawer = new Drawer($targetEl, options);
    // drawer.show()
  }
});

watch(
  () => darkMode.value,
  (value) => {
    if (value) {
      document.documentElement.classList.add("dark");
    } else {
      document.documentElement.classList.remove("dark");
    }
    localStorage.setItem("darkMode", value.toString());
  }
);
onMounted(() => {
  if (process.client) {
    if (darkMode.value) {
      document.documentElement.classList.add("dark");
    } else {
      document.documentElement.classList.remove("dark");
    }
  }
});
</script>
