<template>
    <!-- drawer component -->
    <div
        id="drawer-user-profile"
        class="fixed w-full top-0 right-0 z-40 h-screen p-4 overflow-y-auto transition-transform translate-x-full bg-white w-80 dark:bg-gray-800"
        tabindex="-1"
        aria-labelledby="drawer-user-profile-label"
    >
        <button
            type="button"
            @click="appStore.setShowUserDrawer(false)"
            class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm p-1.5 absolute top-2.5 right-2.5 inline-flex items-center dark:hover:bg-gray-600 dark:hover:text-white"
        >
            <svg
                aria-hidden="true"
                class="w-5 h-5"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
            >
                <path
                    fill-rule="evenodd"
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                    clip-rule="evenodd"
                ></path>
            </svg>
            <span class="sr-only">Close menu</span>
        </button>
        <div class="p-4 pt-10 overflow-y-auto">
            <ProfileCard class="" />
        </div>
    </div>
</template>

<script lang="ts" setup>
import { Drawer } from 'flowbite'
import { onMounted } from 'vue'
import { useAppStore } from '~/stores/app'
import { useAuthStore } from '~~/stores/auth'
import ProfileCard from '~/components/ProfileCard.vue'
import { storeToRefs } from 'pinia'
const appStore = useAppStore()
const authStore = useAuthStore()
const router = useRouter()

const { showUserDrawer } = storeToRefs(appStore)
const { isLoggedIn } = storeToRefs(authStore)
watch(
    () => showUserDrawer.value,
    (value) => {
        if (value) {
            drawer.show()
        } else {
            drawer.hide()
        }
    }
)

const onPush = (path) => {
    router.push(path)
    appStore.setShowUserDrawer(false)
}

let drawer = null
const options: DrawerOptions = {
    placement: 'right',
    backdrop: true,
    bodyScrolling: false,
    edge: false,
    edgeOffset: '',
    backdropClasses: 'bg-gray-900 bg-opacity-50 dark:bg-opacity-80 fixed inset-0 z-30',
    onHide: () => {
        appStore.setShowUserDrawer(false)
    },
    onShow: () => {
        appStore.setShowUserDrawer(true)
    },
}

onMounted(() => {
    if (process.client) {
        const $targetEl = document.getElementById('drawer-user-profile')
        drawer = new Drawer($targetEl, options)
        // drawer.show()
    }
})
</script>
