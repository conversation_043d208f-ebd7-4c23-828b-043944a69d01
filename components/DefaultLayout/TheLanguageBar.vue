<template>
    <section class="w-screen-xl mx-auto">
        <div
            class="sticky flex items-center top-16 border-b border-gray-50 dark:border-gray-900 bg-gray-50 dark:bg-gray-900 flex mb-[2px]"
        >
            <div class="flex-1">
                <LanguageTabs
                    id="from-language-select"
                    :languages="transFromLanguagesList"
                    :activeLanguage="selectedTransFromLang"
                    hasDetectLanguage
                    @onLanguageChange="translateStore.setSelectedTransFromLang"
                    mode="from"
                    :detectedLanguage="detectedLanguage"
                />
            </div>
            <div class="shrink-0">
                <a
                    @click="swapLanguages"
                    data-tooltip-target="tooltip-swaplanguages"
                    class="inline-flex justify-center p-2 text-gray-500 rounded-full cursor-pointer dark:text-gray-400 dark:hover:text-white hover:text-gray-900 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer"
                >
                    <svg
                        class="w-6 h-6"
                        aria-hidden="true"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="1.5"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M7.5 21L3 16.5m0 0L7.5 12M3 16.5h13.5m0-13.5L21 7.5m0 0L16.5 12M21 7.5H7.5"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                        ></path>
                    </svg>
                </a>
                <div
                    id="tooltip-swaplanguages"
                    role="tooltip"
                    class="inline-block absolute invisible z-50 py-2 px-3 text-sm font-medium text-white bg-gray-900 rounded-lg shadow-sm opacity-0 transition-opacity duration-300 tooltip"
                >
                    {{ $t('Swap languages') }}
                    <div class="tooltip-arrow" data-popper-arrow></div>
                </div>
            </div>
            <div class="flex-1">
                <LanguageTabs
                    id="to-language-select"
                    :languages="transToLanguagesList"
                    :activeLanguage="selectedTransToLang"
                    @onLanguageChange="translateStore.setSelectedTransToLang"
                    mode="to"
                />
            </div>
        </div>
        <LanguageOptions />
    </section>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { storeToRefs } from 'pinia'
import LanguageTabs from '~/components/LanguageTabs.vue'
import MobileBottomMenu from '~/components/MobileBottomMenu.vue'
import LimitedWarning from '~/components/LimitedWarning.vue'
import { useTranslateStore } from '~/stores/translate'
import LanguageOptions from '~/components/LanguageOptions.vue'
import BaseSelect from '~/base-components/BaseSelect.vue'
const translateStore = useTranslateStore()

const router = useRouter()

const {
    isTranslating,
    transFromLanguagesList,
    transToLanguagesList,
    selectedTransFromLang,
    selectedTransToLang,
    mode,
    result,
    inputText,
    detectedLanguage,
    isDetectLanguage,
} = storeToRefs(translateStore)

onMounted(() => {
    translateStore.initSelectedLanguages()
})

const swapLanguages = () => {
    if (!isDetectLanguage.value) {
        translateStore.swapLanguages()
        router.push({
            query: {
                from: selectedTransToLang.value,
                to: selectedTransFromLang.value,
            },
        })
    }
}
</script>
