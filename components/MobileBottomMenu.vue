<template>
    <div
        class="fixed md:hidden bottom-0 z-30 w-full -translate-x-1/2 bg-primary-900 border-t border-gray-200 left-1/2 dark:bg-gray-900 dark:border-gray-600"
    >
        <div v-if="!lite" class="w-full">
            <TranslateOptions />
        </div>
        <div class="grid h-full max-w-lg grid-cols-5 mx-auto">
            <button
                data-tooltip-target="tooltip-home"
                type="button"
                @click="router.push({ path: '/' })"
                class="inline-flex flex-col items-center justify-center p-4 hover:bg-primary-700 dark:hover:bg-gray-800 group"
                :class="{ 'bg-primary-700 dark:bg-gray-800 ': route.name === 'index' }"
            >
                <svg
                    class="w-6 h-6 mb-1 text-gray-50 dark:text-gray-400 group-hover:text-primary-50 dark:group-hover:text-primary-500"
                    aria-hidden="true"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="1.5"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path
                        d="M10.5 21l5.25-11.25L21 21m-9-3h7.5M3 5.621a48.474 48.474 0 016-.371m0 0c1.12 0 2.233.038 3.334.114M9 5.25V3m3.334 2.364C11.176 10.658 7.69 15.08 3 17.502m9.334-12.138c.896.061 1.785.147 2.666.257m-4.589 8.495a18.023 18.023 0 01-3.827-5.802"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                    ></path>
                </svg>
                <span class="sr-only">
                    {{ $t('Text') }}
                </span>
            </button>
            <div
                id="tooltip-home"
                role="tooltip"
                class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-white transition-opacity duration-300 bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip dark:bg-gray-700"
            >
                {{ $t('Text') }}
                <div class="tooltip-arrow" data-popper-arrow></div>
            </div>
            <button
                data-tooltip-target="tooltip-bookmark"
                type="button"
                @click="router.push({ path: '/documents' })"
                class="inline-flex flex-col items-center justify-center p-4 hover:bg-primary-700 dark:hover:bg-gray-800 group"
                :class="{ 'bg-primary-700 dark:bg-gray-800 ': route.name === 'documents' }"
            >
                <svg
                    class="w-6 h-6 mb-1 text-gray-50 dark:text-gray-400 group-hover:text-primary-50 dark:group-hover:text-primary-500"
                    aria-hidden="true"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="1.5"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path
                        d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                    ></path>
                </svg>
                <span class="sr-only">{{ $t('Documents') }}</span>
            </button>
            <div
                id="tooltip-bookmark"
                role="tooltip"
                class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-white transition-opacity duration-300 bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip dark:bg-gray-700"
            >
                {{ $t('Documents') }}
                <div class="tooltip-arrow" data-popper-arrow></div>
            </div>
            <div class="flex items-center justify-center">
                <a
                    class="absolute left-1/2 bottom-1 transform -translate-x-1/2 inline-flex justify-center w-20 h-20 rounded-full bg-gray-100 border border-gray-300 dark:bg-gray-800 dark:border-gray-700"
                >
                    <slot></slot>
                    <div v-if="!lite">
                        <!-- <div
                            class="absolute top-5 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-2 h-[50px] bg-primary-100 dark:bg-gray-800"
                        ></div>
                        <div class="absolute left-0 top-[15px] w-[10px] h-2 bg-primary-100 dark:bg-gray-800"></div>
                        <div class="absolute right-0 top-[15px] w-[10px] h-2 bg-primary-100 dark:bg-gray-800"></div> -->
                        <button
                            @click="onTranslate"
                            :disabled="isDisableTranslateButton(route.name)"
                            type="button"
                            class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-primary-500 hover:text-white w-14 h-14 bg-white hover:bg-primary-800 focus:ring-4 focus:outline-none border border-gray-300 focus:ring-primary-300 font-medium rounded-full text-sm p-3.5 text-center inline-flex items-center mr-2 dark:bg-primary-600 dark:text-white dark:hover:bg-gray-200 dark:hover:text-primary-600 dark:focus:ring-primary-800 dark:border-gray-600"
                        >
                            <svg
                                v-if="isTranslating"
                                aria-hidden="true"
                                class="inline w-14 h-14 text-gray-200 animate-spin dark:text-gray-600 fill-primary-500 dark:fill-white"
                                viewBox="0 0 100 101"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                    fill="currentColor"
                                />
                                <path
                                    d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                    fill="currentFill"
                                />
                            </svg>
                            <svg
                                v-else
                                aria-hidden="true"
                                class="w-16 h-16"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    fill-rule="evenodd"
                                    d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
                                    clip-rule="evenodd"
                                ></path>
                            </svg>
                        </button>
                    </div>
                </a>
            </div>
            <div
                id="tooltip-translate"
                role="tooltip"
                class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-white transition-opacity duration-300 bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip dark:bg-gray-700"
            >
                {{ $t('Translate') }}
                <div class="tooltip-arrow" data-popper-arrow></div>
            </div>
            <button
                @click="router.push({ path: '/history' })"
                data-tooltip-target="tooltip-search"
                type="button"
                class="inline-flex flex-col items-center justify-center p-4 hover:bg-primary-700 dark:hover:bg-gray-800 group"
                :class="{ 'bg-primary-700 dark:bg-gray-800 ': route.name === 'history' }"
            >
                <svg
                    class="w-6 h-6 mb-1 text-gray-50 dark:text-gray-400 group-hover:text-primary-50 dark:group-hover:text-primary-500"
                    aria-hidden="true"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="1.5"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path
                        d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                    ></path>
                </svg>
                <span class="sr-only"> {{ $t('History') }}</span>
            </button>
            <div
                id="tooltip-search"
                role="tooltip"
                class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-white transition-opacity duration-300 bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip dark:bg-gray-700"
            >
                {{ $t('History') }}
                <div class="tooltip-arrow" data-popper-arrow></div>
            </div>
            <template v-if="isLoggedIn">
                <button
                    data-tooltip-target="tooltip-profile"
                    type="button"
                    @click="router.push({ path: '/profile' })"
                    class="inline-flex flex-col items-center justify-center p-4 hover:bg-primary-700 dark:hover:bg-gray-800 group"
                >
                    <svg
                        class="w-6 h-6 mb-1 text-gray-50 dark:text-gray-400 group-hover:text-primary-50 dark:group-hover:text-primary-500"
                        aria-hidden="true"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="1.5"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M17.982 18.725A7.488 7.488 0 0012 15.75a7.488 7.488 0 00-5.982 2.975m11.963 0a9 9 0 10-11.963 0m11.963 0A8.966 8.966 0 0112 21a8.966 8.966 0 01-5.982-2.275M15 9.75a3 3 0 11-6 0 3 3 0 016 0z"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                        ></path>
                    </svg>
                    <span class="sr-only">{{ $t('Profile') }}</span>
                </button>
                <div
                    id="tooltip-profile"
                    role="tooltip"
                    class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-white transition-opacity duration-300 bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip dark:bg-gray-700"
                >
                    {{ $t('Profile') }}
                    <div class="tooltip-arrow" data-popper-arrow></div>
                </div>
            </template>
            <template v-else>
                <button
                    data-tooltip-target="tooltip-login"
                    type="button"
                    @click="router.push({ path: '/signin' })"
                    class="inline-flex flex-col items-center justify-center p-4 hover:bg-primary-700 dark:hover:bg-gray-800 group"
                >
                    <svg
                        class="w-6 h-6 mb-1 text-gray-50 dark:text-gray-400 group-hover:text-primary-50 dark:group-hover:text-primary-500"
                        aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 18 16"
                    >
                        <path
                            stroke="currentColor"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M1 8h11m0 0L8 4m4 4-4 4m4-11h3a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h-3"
                        />
                    </svg>
                    <span class="sr-only">{{ $t('Sign In') }}</span>
                </button>
                <div
                    id="tooltip-login"
                    role="tooltip"
                    class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-white transition-opacity duration-300 bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip dark:bg-gray-700"
                >
                    {{ $t('Sign In') }}
                    <div class="tooltip-arrow" data-popper-arrow></div>
                </div>
            </template>
        </div>
    </div>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { useTranslateStore } from '~~/stores/translate'
import { useAuthStore } from '~~/stores/auth'
import TranslateOptions from '~/components/TranslateOptions.vue'
const translateStore = useTranslateStore()
const authStore = useAuthStore()
const { mode } = storeToRefs(translateStore)
const router = useRouter()
const route = useRoute()
const { isTranslating, isDisableTranslateButton } = storeToRefs(translateStore)
const { isLoggingIn, user, isLoggedIn } = storeToRefs(authStore)
const props = defineProps({
    lite: {
        type: Boolean,
        default: false,
    },
})

const onTranslate = () => {
    if (route.name === 'documents' && !isLoggedIn.value) {
        router.push({ path: '/signin' })
    } else {
        translateStore.translate(route.name)
    }
}
</script>
