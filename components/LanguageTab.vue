<template>
    <li class="mr-0">
        <a
            class="inline-block px-4 py-3 rounded-t-lg hover:bg-primary-50 hover:text-gray-600 dark:hover:text-gray-300 dark:hover:bg-gray-700 cursor-pointer"
            :class="{
                'border-b-2 border-primary-600 text-primary-600 dark:border-primary-500 dark:text-primary-500':
                    props.active,
                'hover:bg-gray-300': !props.active,
            }"
        >
            <span v-if="detectedLanguage"> {{ $t(`languages-list.${detectedLanguage}`) }} {{ ' - ' }} {{ $t('Detected') }} </span>
            <span v-else>
                {{ props.text }}
            </span>
        </a>
    </li>
</template>

<script setup lang="ts">
const props = defineProps<{
    text: string
    value: string
    active: boolean
    detectedLanguage: string
}>()
</script>
