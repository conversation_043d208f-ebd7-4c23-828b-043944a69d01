<template>
    <div class="mr-0">
        <a
            @click="ratingThumbsUp"
            data-tooltip-target="tooltip-thumbs-up"
            class="border-0 inline-flex justify-center p-1.5 text-gray-500 rounded-full cursor-pointer dark:text-gray-400 dark:hover:text-white hover:text-gray-900 hover:bg-gray-100 dark:hover:bg-gray-600"
        >
            <span :class="{ fill: liked }" class="material-symbols-outlined"> thumb_up </span>
        </a>
        <div
            id="tooltip-thumbs-up"
            role="tooltip"
            class="inline-block absolute invisible z-50 py-2 px-3 text-sm font-medium text-white bg-gray-900 rounded-lg shadow-sm opacity-0 transition-opacity duration-300 tooltip"
        >
            <span>{{ $t('Thumbs up') }}</span>
            <div class="tooltip-arrow" data-popper-arrow></div>
        </div>
    </div>
    <div>
        <a
            @click="ratingThumbsDown"
            data-tooltip-target="tooltip-thumbs-down"
            class="border-0 inline-flex justify-center p-1.5 text-gray-500 rounded-full cursor-pointer dark:text-gray-400 dark:hover:text-white hover:text-gray-900 hover:bg-gray-100 dark:hover:bg-gray-600"
        >
            <span :class="{ fill: disLiked }" class="material-symbols-outlined"> thumb_down </span>
        </a>
        <div
            id="tooltip-thumbs-down"
            role="tooltip"
            class="inline-block absolute invisible z-50 py-2 px-3 text-sm font-medium text-white bg-gray-900 rounded-lg shadow-sm opacity-0 transition-opacity duration-300 tooltip"
        >
            <span>{{ $t('Thumbs down') }}</span>
            <div class="tooltip-arrow" data-popper-arrow></div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import { useHistoryStore } from '~/stores/history'
import FeedbackModal from '~/base-components/FeedbackModal.vue'
import { ModalsContainer, useModal } from 'vue-final-modal'

const historyStore = useHistoryStore()

const props = defineProps<{
    uuid: string
    rating: string
    flat: boolean
}>()

const emit = defineEmits(['onRated'])

const liked = ref(props.rating === 'thumbs_up')
const disLiked = ref(props.rating === 'thumbs_down')
const feedbackMode = ref('dislike')

watch(
    () => props.rating,
    (val) => {
        liked.value = val === 'thumbs_up'
        disLiked.value = val === 'thumbs_down'
    }
)

const { open, close, options } = useModal({
    component: FeedbackModal,
    attrs: {
        onClose() {
            close()
        },
        onSubmit() {
            close()
        },
        type: feedbackMode.value,
        uuid: props.uuid,
    },
})

const ratingThumbsUp = async () => {
    const res = await historyStore.rateHistory(props.uuid, {
        rating: liked.value ? null : 'thumbs_up',
    })
    if (res) {
        disLiked.value = false
        liked.value = !liked.value

        emit('onRated', {
            liked: liked.value,
            disLiked: disLiked.value,
        })

        options.attrs.type = 'like'
        if (liked.value) {
            open()
        }
    }
}
const ratingThumbsDown = async () => {
    const res = await historyStore.rateHistory(props.uuid, {
        rating: disLiked.value ? null : 'thumbs_down',
    })
    if (res) {
        liked.value = false
        disLiked.value = !disLiked.value

        emit('onRated', {
            liked: liked.value,
            disLiked: disLiked.value,
        })

        options.attrs.type = 'dislike'
        if (disLiked.value) {
            open()
        }
    }
}
</script>
