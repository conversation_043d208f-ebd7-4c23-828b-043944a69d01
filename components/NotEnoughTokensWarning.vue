<template>
    <div
        class="relative p-4 mb-4 text-yellow-800 border border-yellow-300 rounded-lg bg-yellow-50 dark:bg-gray-800 dark:text-yellow-300 dark:border-yellow-800"
        role="alert"
    >
        <div class="flex items-center">
            <svg
                aria-hidden="true"
                class="w-5 h-5 mr-2"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
            >
                <path
                    fill-rule="evenodd"
                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                    clip-rule="evenodd"
                ></path>
            </svg>
            <span class="sr-only">Info</span>
            <h3 class="text-lg font-medium">{{ $t('Not enough tokens') }}</h3>
        </div>
        <div class="mt-2 mb-4 text-sm">
            <p>
                {{
                    $t(
                        'Your tokens are not enough for next translation. Please add more tokens.'
                    )
                }}
            </p>
        </div>
        <div class="flex flex-wrap gap-2">
            <button
                @click="router.push({ path: '/profile/add-tokens' })"
                type="button"
                class="text-white bg-yellow-800 hover:bg-yellow-900 focus:ring-4 focus:outline-none focus:ring-yellow-300 font-medium rounded-lg text-xs px-3 py-1.5 text-center inline-flex items-center dark:bg-yellow-300 dark:text-gray-800 dark:hover:bg-yellow-400 dark:focus:ring-yellow-800"
            >
                {{ $t('Add tokens') }}

                <svg
                    aria-hidden="true"
                    class="ml-2 h-4 w-4"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="1.5"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path
                        d="M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                    ></path>
                </svg>
            </button>

            <button
                @click="purchaseUnlimitedPlan"
                type="button"
                class="text-white bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 focus:ring-4 focus:outline-none focus:ring-purple-300 font-medium rounded-lg text-xs px-3 py-1.5 text-center inline-flex items-center dark:focus:ring-purple-800"
            >
                {{ $t('Get Unlimited ($10)') }}

                <svg
                    aria-hidden="true"
                    class="ml-2 h-4 w-4"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="1.5"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path
                        d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                    ></path>
                </svg>
            </button>
        </div>
    </div>
</template>

<script lang="ts" setup>
import PaymentModal from '~/base-components/PaymentModal.vue'
import { useAuthStore } from '~/stores/auth'
import { storeToRefs } from 'pinia'
import { useModal } from 'vue-final-modal'

const router = useRouter()
const emit = defineEmits(['dismiss'])

// Payment modal setup
const { open, close, options } = useModal({
  component: PaymentModal,
  attrs: {
    onClose() {
      close();
    },
    onSubmit() {
      close();
    },
  },
});

// Stores
const authStore = useAuthStore()
const { isLoggedIn } = storeToRefs(authStore)

// Extend feature product (BP0001)
const extendFeatureProduct = ref({
  product_id: 'BP0001',
  price: 10,
  name: 'GPT-4o-mini Unlimited'
})

const purchaseUnlimitedPlan = () => {
  if (!isLoggedIn.value) {
    router.push("/signin");
    return;
  }

  // @ts-ignore
  options.attrs.type = "extend-feature";
  // @ts-ignore
  options.attrs.plan = extendFeatureProduct.value;
  // @ts-ignore
  options.attrs.id = extendFeatureProduct.value.product_id;
  // @ts-ignore
  options.attrs.quantity = 1;
  open();
}
</script>
