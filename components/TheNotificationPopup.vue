<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { useAppStore } from '~/stores/app'
import { useAuthStore } from '~/stores/auth'
import { onMounted, watch } from 'vue'
import { Modal } from 'flowbite'
import type { ModalOptions, ModalInterface } from 'flowbite'
import dayjs from 'dayjs'
const appStore = useAppStore()
const authStore = useAuthStore()
const { showNotificationPopup } = storeToRefs(appStore)
const { user, hasActiveExtendFeature } = storeToRefs(authStore)
const config = useRuntimeConfig()
let modal: ModalInterface
onMounted(() => {
    if (process.client) {
        const $modalElement: HTMLElement = document.querySelector('#modalNotificationPopup')
        const modalOptions: ModalOptions = {
            placement: 'center-center',
            backdrop: 'dynamic',
            backdropClasses: 'bg-gray-900 bg-opacity-50 dark:bg-opacity-80 fixed inset-0 z-40',
            closable: false,
            onHide: () => {},
            onShow: () => {},
        }

        modal = new Modal($modalElement, modalOptions)

        if (showNotificationPopup.value && user.value) modal.show()
    }
})

const checkLastSeenNotificationPopupWithin8Hours = () => {
    const lastSeenNotificationPopup = localStorage.getItem('lastSeenNotificationPopup')
    if (lastSeenNotificationPopup) {
        const diff = dayjs().diff(dayjs(lastSeenNotificationPopup), 'hour')
        return diff < 8
    }
    return false
}

const checkLastSeenNotificationPopupWithinHours = (hours: number) => {
    const lastSeenNotificationPopup = localStorage.getItem('lastSeenNotificationPopup')
    if (lastSeenNotificationPopup) {
        const diff = dayjs().diff(dayjs(lastSeenNotificationPopup), 'hour')
        return diff < hours
    }
    return false
}

watch(user, (value) => {
    // const seenNotificationPopup = localStorage.getItem('seenNotificationPopup') === 'true'
    //check user seen notification popup within 8 hours
    // check if user haven't buy Extend Feature
    const seenNotificationPopup = checkLastSeenNotificationPopupWithinHours(1)
    if (value && value.current_plan === 'FP0001' && config.public.NUXT_SHOW_NOTIFICATION && !seenNotificationPopup && !hasActiveExtendFeature.value)
        showNotificationPopup.value = true
})

watch(showNotificationPopup, (value) => {
    const seenNotificationPopup = checkLastSeenNotificationPopupWithinHours(1)
    if (value && !seenNotificationPopup) {
        modal.show()
    } else {
        modal.hide()
        localStorage.setItem('lastSeenNotificationPopup', dayjs().toISOString())
    }
})

const navigationToPage = (path: string) => {
    navigateTo(path)
    showNotificationPopup.value = false
}
</script>
<template>
    <!-- Main modal -->
    <div
        id="modalNotificationPopup"
        tabindex="-1"
        aria-hidden="true"
        class="scrollbar-thin fixed top-0 left-0 right-0 z-50 hidden w-full p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full"
    >
        <div class="relative w-full max-w-xl max-h-full">
            <!-- Modal content -->
            <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                <!-- Modal header -->
                <div class="flex items-start justify-between p-4 border-b rounded-t dark:border-gray-600">
                    <h3 class="text-lg font-semibold text-purple-800 dark:text-purple-200">
                        {{ $t('🎉 Special Offer: GPT-4o-mini Unlimited!') }}
                    </h3>
                    <button
                        @click="
                            () => {
                                showNotificationPopup = false
                            }
                        "
                        type="button"
                        class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm p-1.5 ml-auto inline-flex items-center dark:hover:bg-gray-600 dark:hover:text-white"
                    >
                        <svg
                            aria-hidden="true"
                            class="w-5 h-5"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                fill-rule="evenodd"
                                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                clip-rule="evenodd"
                            ></path>
                        </svg>
                        <span class="sr-only">Close modal</span>
                    </button>
                </div>
                <!-- Modal body -->
                <div>
                    <section class="bg-white dark:bg-gray-700 dark:text-gray-300 text-sm py-4">
                        <div class="p-4 flex flex-col">
                            <!-- Special Offer Badge -->
                            <div class="flex justify-center mb-4">
                                <span class="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-4 py-2 rounded-full text-sm font-semibold">
                                    {{ $t('Limited Time Offer') }}
                                </span>
                            </div>

                            <!-- Price Display -->
                            <div class="text-center mb-4">
                                <div class="flex justify-center items-baseline">
                                    <span class="text-4xl font-extrabold text-purple-700 dark:text-purple-300">$10</span>
                                    <span class="text-gray-500 dark:text-gray-400 ml-1">/{{ $t('year') }}</span>
                                </div>
                                <p class="text-lg font-semibold text-purple-800 dark:text-purple-200 mt-2">
                                    {{ $t('GPT-4o-mini Unlimited') }}
                                </p>
                            </div>

                            <p class="mb-4 text-center">
                                {{ $t('Get unlimited access to GPT-4o-mini for an entire year! Perfect for users who want consistent, high-quality translations without worrying about token limits.') }}
                            </p>

                            <!-- Features List -->
                            <div class="mb-4">
                                <ul class="space-y-2 text-sm">
                                    <li class="flex items-center space-x-2">
                                        <svg class="w-4 h-4 text-purple-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                        <span>{{ $t('Unlimited GPT-4o-mini usage for 1 year') }}</span>
                                    </li>
                                    <li class="flex items-center space-x-2">
                                        <svg class="w-4 h-4 text-purple-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                        <span>{{ $t('Works even with 0 tokens') }}</span>
                                    </li>
                                    <li class="flex items-center space-x-2">
                                        <svg class="w-4 h-4 text-purple-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                        <span>{{ $t('No credit lock for document translation') }}</span>
                                    </li>
                                </ul>
                            </div>

                            <div class="flex items-center space-x-1 mt-4 mb-3">
                                <svg
                                    class="w-5 h-5 text-purple-500 dark:text-purple-400"
                                    fill="currentColor"
                                    viewBox="0 0 20 20"
                                >
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                </svg>
                                <div class="text-md text-purple-600 dark:text-purple-400 font-semibold">
                                    {{ $t('Get Started Today!') }}
                                </div>
                            </div>

                            <div class="flex flex-col space-y-2">
                                <button
                                    @click="navigationToPage('/pricing-plans')"
                                    type="button"
                                    class="w-full text-white bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 focus:ring-4 focus:outline-none focus:ring-purple-300 dark:focus:ring-purple-800 shadow-lg font-medium rounded-lg text-sm px-5 py-3 text-center"
                                >
                                    {{ $t('Get GPT-4o-mini Unlimited - $10/year') }}
                                </button>
                                <button
                                    @click="navigationToPage('/pricing-plans')"
                                    type="button"
                                    class="w-full text-purple-700 bg-purple-100 hover:bg-purple-200 focus:ring-4 focus:outline-none focus:ring-purple-300 dark:bg-purple-800 dark:text-purple-200 dark:hover:bg-purple-700 dark:focus:ring-purple-800 font-medium rounded-lg text-sm px-5 py-2 text-center"
                                >
                                    {{ $t('View All Plans') }}
                                </button>
                            </div>

                            <p class="mt-4 text-center text-xs text-gray-500 dark:text-gray-400">
                                {{ $t('One-time payment for 1 year unlimited access to GPT-4o-mini model.') }}
                            </p>
                        </div>
                    </section>
                </div>
            </div>
        </div>
    </div>
</template>
