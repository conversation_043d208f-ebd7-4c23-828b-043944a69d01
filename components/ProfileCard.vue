<template>
    <div class="w-full md:max-w-xs mx-auto">
        <div v-if="user" class="flex flex-col items-center pb-10">
            <div
                class="shadow-lg mb-4 border relative inline-flex items-center justify-center w-24 h-24 overflow-hidden bg-primary-100 rounded-full dark:bg-primary-600"
            >
                <img v-if="user.avatar" class="w-24 h-24 rounded-full" :src="user.avatar" alt="Rounded avatar" />
                <span v-else class="font-medium text-2xl text-gray-600 dark:text-gray-300 uppercase">
                    {{ user.full_name.substring(0, 2) }}
                </span>
            </div>
            <h5 class="mb-1 text-xl font-medium text-gray-900 dark:text-white truncate max-w-full">
                {{ user.full_name }}
            </h5>
            <span class="text-sm text-gray-500 dark:text-gray-400">{{ user.email }}</span>
            <div v-if="user?.user_plan?.subscription_id" class="mt-2">
                <span
                    class="bg-yellow-100 text-yellow-800 text-xs font-medium inline-flex items-center px-2.5 py-0.5 rounded-full dark:bg-gray-700 dark:text-yellow-400 border border-yellow-400"
                >
                    <svg
                        class="w-2.5 h-2.5 mr-1.5"
                        aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                    >
                        <path
                            fill="currentColor"
                            d="m18.774 8.245-.892-.893a1.5 1.5 0 0 1-.437-1.052V5.036a2.484 2.484 0 0 0-2.48-2.48H13.7a1.5 1.5 0 0 1-1.052-.438l-.893-.892a2.484 2.484 0 0 0-3.51 0l-.893.892a1.5 1.5 0 0 1-1.052.437H5.036a2.484 2.484 0 0 0-2.48 2.481V6.3a1.5 1.5 0 0 1-.438 1.052l-.892.893a2.484 2.484 0 0 0 0 3.51l.892.893a1.5 1.5 0 0 1 .437 1.052v1.264a2.484 2.484 0 0 0 2.481 2.481H6.3a1.5 1.5 0 0 1 1.052.437l.893.892a2.484 2.484 0 0 0 3.51 0l.893-.892a1.5 1.5 0 0 1 1.052-.437h1.264a2.484 2.484 0 0 0 2.481-2.48V13.7a1.5 1.5 0 0 1 .437-1.052l.892-.893a2.484 2.484 0 0 0 0-3.51Z"
                        />
                        <path
                            fill="#fff"
                            d="M8 13a1 1 0 0 1-.707-.293l-2-2a1 1 0 1 1 1.414-1.414l1.42 1.42 5.318-3.545a1 1 0 0 1 1.11 1.664l-6 4A1 1 0 0 1 8 13Z"
                        />
                    </svg>
                    {{ $t('Monthly Subscription') }}
                </span>
            </div>
            <div v-else-if="user?.user_plan?.is_subscription_processing" class="mt-2">
                <span
                    class="bg-primary-100 text-primary-800 text-xs font-medium inline-flex items-center px-2.5 py-0.5 rounded-full dark:bg-gray-700 dark:text-primary-400 border border-primary-400"
                >
                    <svg
                        class="w-2.5 h-2.5 mr-1.5"
                        aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 16 20"
                    >
                        <path
                            stroke="currentColor"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M15 1H1m14 18H1m2 0v-4.333a2 2 0 0 1 .4-1.2L5.55 10.6a1 1 0 0 0 0-1.2L3.4 6.533a2 2 0 0 1-.4-1.2V1h10v4.333a2 2 0 0 1-.4 1.2L10.45 9.4a1 1 0 0 0 0 1.2l2.15 2.867a2 2 0 0 1 .4 1.2V19H3Z"
                        />
                    </svg>
                    {{ $t('Subscription is processing...') }}
                </span>
            </div>

            <!-- Extend Feature Badge -->
            <div v-if="hasActiveExtendFeature" class="mt-2">
                <span
                    class="bg-gradient-to-r from-purple-100 to-blue-100 text-purple-800 text-xs font-medium inline-flex items-center px-2.5 py-0.5 rounded-full dark:bg-gradient-to-r dark:from-purple-900 dark:to-blue-900 dark:text-purple-300 border border-purple-300 dark:border-purple-700"
                >
                    <svg
                        class="w-2.5 h-2.5 mr-1.5"
                        aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                    >
                        <path
                            d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM10 15a1 1 0 1 1 0-2 1 1 0 0 1 0 2Zm1-4a1 1 0 0 1-2 0V6a1 1 0 0 1 2 0v5Z"
                        />
                    </svg>
                    {{ $t('GPT-4o-mini Unlimited') }}
                </span>
                <div class="text-xs text-purple-600 dark:text-purple-400 mt-1 text-center">
                    {{ $t('Valid until') }}: {{ extendFeatureExpireDate }}
                </div>
            </div>
            <ul class="space-y-2 text-sm font-base w-full mt-4 mb-2">
                <li>
                    <a
                        href="#"
                        class="group flex items-center p-2 text-gray-900 rounded-lg dark:text-white justify-between"
                    >
                        <div
                            v-if="user?.user_plan?.subscription_id && props.canCancelSubscription"
                            class="hidden group-hover:flex"
                            @click="open"
                        >
                            <svg
                                class="flex-shrink-0 w-5 h-5 text-red-500 transition duration-75 dark:text-red-400"
                                aria-hidden="true"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 20 20"
                            >
                                <path
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="2"
                                    d="m13 7-6 6m0-6 6 6m6-3a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                                />
                            </svg>
                            <span class="flex-1 ml-3 whitespace-nowrap text-red-500 dark:text-red-400">{{
                                $t('Cancel subscription')
                            }}</span>
                        </div>
                        <div
                            class="relative flex"
                            :class="
                                user?.user_plan?.subscription_id && props.canCancelSubscription
                                    ? 'group-hover:hidden'
                                    : ''
                            "
                        >
                            <svg
                                class="flex-shrink-0 w-5 h-5 text-gray-500 transition duration-75 dark:text-gray-400"
                                aria-hidden="true"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 20 20"
                            >
                                <path
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                    d="m7 10 2 2 4-4m6 2a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                                />
                            </svg>
                            <span class="flex-1 ml-3 whitespace-nowrap">{{ $t('Current plan') }}</span>
                        </div>
                        <span
                            class="inline-flex items-center justify-center px-2 ml-3 text-sm font-medium text-gray-800 bg-yellow-100 rounded-full dark:bg-yellow-700 dark:text-gray-300"
                        >
                            {{ $t(user?.current_plan) }}
                        </span>
                    </a>
                </li>
                <li>
                    <a href="#" class="flex items-center p-2 text-gray-900 rounded-lg dark:text-white">
                        <svg
                            class="flex-shrink-0 w-5 h-5 text-gray-500 transition duration-75 dark:text-gray-400"
                            aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 21 20"
                        >
                            <path
                                stroke="currentColor"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="1.5"
                                d="m11.479 1.712 2.367 4.8a.532.532 0 0 0 .4.292l5.294.769a.534.534 0 0 1 .3.91l-3.83 3.735a.534.534 0 0 0-.154.473l.9 5.272a.535.535 0 0 1-.775.563l-4.734-2.49a.536.536 0 0 0-.5 0l-4.73 2.487a.534.534 0 0 1-.775-.563l.9-5.272a.534.534 0 0 0-.154-.473L2.158 8.48a.534.534 0 0 1 .3-.911l5.294-.77a.532.532 0 0 0 .4-.292l2.367-4.8a.534.534 0 0 1 .96.004Z"
                            />
                        </svg>
                        <span class="flex-1 ml-3 whitespace-nowrap">{{ $t('Available tokens') }}</span>
                        <span
                            class="inline-flex items-center justify-center px-2 ml-3 text-sm font-medium text-gray-800 bg-blue-100 rounded-full dark:bg-blue-700 dark:text-gray-300"
                        >
                            {{ formatUserTokens }}
                        </span>
                    </a>
                </li>
                <li>
                    <a href="#" class="flex items-center p-2 text-gray-900 rounded-lg dark:text-white">
                        <svg
                            class="flex-shrink-0 w-5 h-5 text-gray-500 transition duration-75 dark:text-gray-400"
                            aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 20 20"
                        >
                            <path
                                stroke="currentColor"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M11 8H2a1 1 0 0 0-1 1v9a1 1 0 0 0 1 1h7m2.5-11V4.5a3.5 3.5 0 1 0-7 0V8m10 5.217V14.5l.9.9m3.6-.9a4.5 4.5 0 1 1-9 0 4.5 4.5 0 0 1 9 0Z"
                            />
                        </svg>

                        <span class="flex-1 ml-3 whitespace-nowrap">{{ $t('Locked tokens') }}</span>
                        <span
                            class="inline-flex items-center justify-center px-2 ml-3 text-sm font-medium text-gray-800 bg-pink-100 rounded-full dark:bg-pink-700 dark:text-gray-300"
                        >
                            {{ formatUserLockedTokens }}
                        </span>
                    </a>
                </li>
                <!-- <li>
                    <a href="#" class="flex items-center p-2 text-gray-900 rounded-lg dark:text-white">
                        <svg
                            class="flex-shrink-0 w-5 h-5 text-gray-500 transition duration-75 dark:text-gray-400"
                            aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 20 20"
                        >
                            <path
                                stroke="currentColor"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="1.5"
                                d="M5 1v3m5-3v3m5-3v3M1 7h18M5 11h10M2 3h16a1 1 0 0 1 1 1v14a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1Z"
                            />
                        </svg>
                        <span class="flex-1 ml-3 whitespace-nowrap">{{ $t('Expiration date') }}</span>
                        <span
                            class="inline-flex items-center justify-center px-2 ml-3 text-sm font-medium text-gray-800 bg-gray-100 rounded-full dark:bg-gray-700 dark:text-gray-300"
                        >
                            {{ expirationFormat }}
                        </span>
                    </a>
                </li> -->
            </ul>
            <div class="w-full grid grid-cols-2 mt-4 gap-4 md:mt-6">
                <a
                    @click="onRedirect('/profile/add-tokens')"
                    class="cursor-pointer inline-flex items-center px-4 py-2 text-sm font-medium text-center focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                >
                    <svg
                        class="w-4 h-4 mr-2 -ml-1"
                        aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 19 20"
                    >
                        <path
                            stroke="currentColor"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M6 15a2 2 0 1 0 0 4 2 2 0 0 0 0-4Zm0 0h8m-8 0-1-4m9 4a2 2 0 1 0 0 4 2 2 0 0 0 0-4Zm1-4H5m0 0L3 4m0 0h5.501M3 4l-.792-3H1m11 3h6m-3 3V1"
                        />
                    </svg>
                    {{ $t('Add tokens') }}
                </a>
                <a
                    @click="onRedirect('/pricing-plans')"
                    class="cursor-pointer inline-flex items-center px-4 py-2 text-sm font-medium text-center focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                >
                    <svg
                        class="w-4 h-4 mr-2 -ml-1"
                        aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 20 18"
                    >
                        <path
                            stroke="currentColor"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="m1 14 3-3m-3 3 3 3m-3-3h16v-3m2-7-3 3m3-3-3-3m3 3H3v3"
                        />
                    </svg>
                    {{ $t('Change plan') }}
                </a>
                <a
                    @click="onRedirect('/profile/payment-history')"
                    class="col-span-2 cursor-pointer inline-flex items-center px-4 py-2 text-sm font-medium text-center focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                >
                    <svg
                        class="w-4 h-4 mr-2 -ml-1"
                        aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 16 20"
                    >
                        <path
                            stroke="currentColor"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="1.5"
                            d="M6 1v4a1 1 0 0 1-1 1H1m8-2h3M9 7h3m-4 3v6m-4-3h8m3-11v16a.969.969 0 0 1-.932 1H1.934A.97.97 0 0 1 1 18V5.828a2 2 0 0 1 .586-1.414l2.828-2.828A2 2 0 0 1 5.829 1h8.239A.969.969 0 0 1 15 2ZM4 10h8v6H4v-6Z"
                        />
                    </svg>

                    {{ $t('Payment history') }}
                </a>
                <a
                    @click="onRedirect('/profile/')"
                    class="col-span-2 cursor-pointer inline-flex items-center px-4 py-2 text-sm font-medium text-center focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                >
                    <svg
                        class="w-4 h-4 mr-2 -ml-1"
                        aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 20 16"
                    >
                        <path
                            stroke="currentColor"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="1.5"
                            d="M3.656 12.115a3 3 0 0 1 5.682-.015M13 5h3m-3 3h3m-3 3h3M2 1h16a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1Zm6.5 4.5a2 2 0 1 1-4 0 2 2 0 0 1 4 0Z"
                        />
                    </svg>
                    {{ $t('Update account') }}
                </a>
                <a
                    @click="onLogout"
                    class="col-span-2 cursor-pointer inline-flex items-center px-4 py-2 text-sm font-medium text-center focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                >
                    <svg
                        class="w-4 h-4 mr-2 -ml-1"
                        aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 16 16"
                    >
                        <path
                            stroke="currentColor"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="1.5"
                            d="M4 8h11m0 0-4-4m4 4-4 4m-5 3H3a2 2 0 0 1-2-2V3a2 2 0 0 1 2-2h3"
                        />
                    </svg>
                    {{ $t('Sign out') }}
                </a>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { storeToRefs } from 'pinia'
import { useAuthStore } from '~~/stores/auth'
import { useAppStore } from '~~/stores/app'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import utc from 'dayjs/plugin/utc'
import CancelSubscriptionConfirmation from '~/base-components/CancelSubscriptionConfirmation.vue'
import { ModalsContainer, useModal } from 'vue-final-modal'
import { usePaymentsStore } from '~/stores/payments'
const paymentsStore = usePaymentsStore()
dayjs.locale(window.localStorage.getItem('locale') || 'en')
dayjs.extend(utc)
dayjs.extend(relativeTime)
const { $abbreviatedUnit } = useNuxtApp()
const authStore = useAuthStore()
const appStore = useAppStore()
const { isLoggingIn, user, isUsingFreePlan } = storeToRefs(authStore)
const { loading } = storeToRefs(paymentsStore)
const router = useRouter()
const formatUserTokens = computed(() => {
    return new Intl.NumberFormat().format(user.value?.tokens || 0)
})

const props = defineProps({
    canCancelSubscription: {
        type: Boolean,
        default: false,
    },
})

const { open, close } = useModal({
    component: CancelSubscriptionConfirmation,
    attrs: {
        async onConfirm() {
            const result = await paymentsStore.cancelSubscription({
                subscription_id: user.value?.user_plan?.subscription_id,
            })
            await authStore.syncUserTokenInfo()
            if (result) {
                appStore.setShowUserDrawer(false)
                router.push({ path: '/profile/cancel-successful' })
            }
            close()
        },
        onCancel() {
            close()
        },
    },
})

const formatUserLockedTokens = computed(() => {
    return new Intl.NumberFormat().format(user.value?.user_token?.locked_token || 0)
})

// Check if user has active extend feature (BP0001)
const hasActiveExtendFeature = computed(() => {
    if (!user.value?.user_benefits) return false

    const extendFeatureBenefit = user.value.user_benefits.find(
        benefit => benefit.product.id === 'BP0001'
    )

    if (!extendFeatureBenefit) return false

    // Check if not expired
    const now = dayjs()
    const expireDate = dayjs(extendFeatureBenefit.expire_at)

    return expireDate.isAfter(now)
})

// Get extend feature expire date
const extendFeatureExpireDate = computed(() => {
    if (!user.value?.user_benefits) return ''

    const extendFeatureBenefit = user.value.user_benefits.find(
        benefit => benefit.product.id === 'BP0001'
    )

    if (!extendFeatureBenefit) return ''

    return dayjs(extendFeatureBenefit.expire_at).format('YYYY-MM-DD')
})

// const expirationFormat = computed(() => dayjs().to(dayjs().add(69, "day")));
const expirationFormat = computed(() =>
    user.value?.user_plan?.expire_at
        ? dayjs.unix(dayjs.utc(user.value?.user_plan?.expire_at).unix()).format('YYYY-MM-DD HH:mm')
        : ''
)
const onRedirect = (to: string) => {
    router.push(to)
    appStore.setShowUserDrawer(false)
}

const onLogout = () => {
    authStore.logout()
    appStore.setShowUserDrawer(false)
}
</script>
