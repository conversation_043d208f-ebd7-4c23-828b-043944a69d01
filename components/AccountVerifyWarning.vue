<template>
    <div
        class="relative p-4 mb-4 text-yellow-800 border border-yellow-300 rounded-lg bg-yellow-50 dark:bg-gray-800 dark:text-yellow-300 dark:border-yellow-800"
        role="alert"
    >
        <div class="flex items-center">
            <svg
                aria-hidden="true"
                class="w-5 h-5 mr-2"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
            >
                <path
                    fill-rule="evenodd"
                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                    clip-rule="evenodd"
                ></path>
            </svg>
            <span class="sr-only">Info</span>
            <h3 class="text-lg font-medium">{{ $t('Your account is not verified.') }}</h3>
        </div>
        <div class="mt-2 mb-4 text-sm">
            <p>
                {{
                    $t(
                        'Please verify your account. If you did not receive the verification email, please click the button below to resend the verification email.'
                    )
                }}
            </p>
        </div>
        <div class="flex">
            <button
                @click="onResendVerifyEmail"
                type="button"
                class="text-white bg-yellow-800 hover:bg-yellow-900 focus:ring-4 focus:outline-none focus:ring-yellow-300 font-medium rounded-lg text-xs px-3 py-1.5 mr-2 text-center inline-flex items-center dark:bg-yellow-300 dark:text-gray-800 dark:hover:bg-yellow-400 dark:focus:ring-yellow-800"
                :disabled="resendDone"
            >
                <span v-if="isResendingActivation">
                    {{ $t('Sending...') }}
                </span>
                <span v-else>
                    {{
                        resendDone
                            ? $t('Resendable in {second} seconds', { second: resendableCountdown })
                            : $t('Resend verify email')
                    }}
                </span>

                <svg
                    v-if="!resendDone && !isResendingActivation"
                    aria-hidden="true"
                    class="ml-2 h-4 w-4"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="1.5"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path
                        d="M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                    ></path>
                </svg>
            </button>
        </div>
    </div>
</template>

<script lang="ts" setup>
const router = useRouter()
const emit = defineEmits(['dismiss'])
import { useAuthStore } from '~/stores/auth'
const authStore = useAuthStore()
import { storeToRefs } from 'pinia'

const { isResendingActivation } = storeToRefs(authStore)

const resendDone = ref(false)
const resendableCountdown = ref(15)
const onResendVerifyEmail = async () => {
    resendableCountdown.value = 15
    await authStore.resendVerifyEmail()
    resendDone.value = true
    const timer = setInterval(() => {
        resendableCountdown.value--
        if (resendableCountdown.value <= 0) {
            clearInterval(timer)
            resendDone.value = false
        }
    }, 1000)
}
</script>
