<template>
    <div class="text-sm font-medium text-center text-gray-500 dark:text-gray-400 dark:border-gray-700">
        <ul class="hidden lg:flex flex flex-wrap -mb-px">
            <LanguageTab
                v-if="hasDetectLanguage"
                @click="changeLanguage('Detect language')"
                :text="$t(`Detect language`)"
                :active="activeLanguage === 'Detect language'"
                :detectedLanguage="detectedLanguage"
            />
            <LanguageTab
                v-for="language in languages"
                :text="$t(`languages-list.${language}`)"
                :active="language === activeLanguage"
                @click="changeLanguage(language)"
            />
            <a id="mega-menu-full-dropdown-button" class="hidden"></a>
            <a
                :id="`btn-toggle-select-language-${mode}`"
                v-if="openSelectLanguage && selectLanguageFor === mode"
                @click="closeSelectLanguage"
                class="inline-flex justify-center p-3 text-gray-500 rounded-full cursor-pointer dark:text-gray-400 dark:hover:text-white hover:text-gray-900 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer"
            >
                <svg
                    class="w-5 h-5 rotate-180 animate-slow-up"
                    aria-hidden="true"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="1.5"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path d="M19.5 8.25l-7.5 7.5-7.5-7.5" stroke-linecap="round" stroke-linejoin="round"></path>
                </svg>
            </a>
            <a
                v-else
                :id="`btn-toggle-select-language-${mode}`"
                @click="toggleSelectLanguage"
                class="inline-flex justify-center p-3 text-gray-500 rounded-full cursor-pointer dark:text-gray-400 dark:hover:text-white hover:text-gray-900 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer"
            >
                <svg
                    class="w-5 h-5 rotate-0 animate-slow-down"
                    aria-hidden="true"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="1.5"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path d="M19.5 8.25l-7.5 7.5-7.5-7.5" stroke-linecap="round" stroke-linejoin="round"></path>
                </svg>
            </a>
        </ul>
        <div
            @click="toggleSelectLanguage"
            class="lg:hidden cursor-pointer text-primary-800 hover:text-primary-500 dark:text-primary-500 dark:hover:text-gray-200 py-4"
        >
            <tempate v-if="activeLanguage === 'Detect language'">
                {{ detectedLanguage ? `${detectedLanguage} - ${$t('Detected')}` : $t(`Detect language`) }}
            </tempate>
            <template v-else>
                {{ $t(`languages-list.${activeLanguage}`) }}
            </template>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useTranslateStore } from '~~/stores/translate'
import LanguageTab from '~/components/LanguageTab.vue'
import { storeToRefs } from 'pinia'
const translateStore = useTranslateStore()
const { openSelectLanguage, selectLanguageFor } = storeToRefs(translateStore)
const props = defineProps<{
    languages: string
    hasDetectLanguage: boolean
    activeLanguage: string
    mode: string
    detectedLanguage: string
}>()

const emit = defineEmits<{
    onLanguageChange: (language: string) => void
}>()

const changeLanguage = (language: string) => {
    translateStore.selectLanguage(props.mode, language)
}

const closeSelectLanguage = () => {
    translateStore.setOpenSelectLanguage(false)
}

const toggleSelectLanguage = () => {
    translateStore.setSelectLanguageFor(props.mode)
}
</script>
