<template>
    <li class="mb-10 ml-6">
        <span
            class="absolute flex items-center justify-center w-6 h-6 bg-primary-100 rounded-full -left-3 ring-2 ring-white dark:ring-gray-900 dark:bg-primary-900"
            :class="{
                'bg-green-100 dark:bg-green-900': props.isSuccess,
                'bg-red-100 dark:bg-red-900': !props.isSuccess,
            }"
        >
            <svg
                v-if="props.isSuccess"
                class="w-2.5 h-2.5 text-gray-800 dark:text-gray-300"
                aria-hidden="true"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 16 12"
            >
                <path
                    stroke="currentColor"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M1 5.917 5.724 10.5 15 1.5"
                />
            </svg>
            <svg
                v-else
                class="w-2.5 h-2.5 text-gray-800 dark:text-gray-300"
                aria-hidden="true"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 16 16"
            >
                <path
                    stroke="currentColor"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M1 1l14 14M15 1L1 15"
                />
            </svg>
        </span>
        <div class="flex flex-inline items-center justify-between">
            <h3 class="flexmb-1 text-lg font-semibold text-gray-900 dark:text-white">
                {{ $t(props.order_product.id) }}
                <span
                    class="bg-gradient-to-r from-purple-100 to-blue-100 text-purple-800 text-sm font-medium mr-2 px-2.5 py-0.5 rounded dark:bg-gradient-to-r dark:from-purple-900 dark:to-blue-900 dark:text-purple-300 ml-2"
                >
                    {{ $t('Extend Feature') }}
                </span>
                <span
                    v-if="props.status && props.isSuccess"
                    class="bg-primary-100 text-primary-800 text-sm font-medium mr-2 px-2.5 py-0.5 rounded dark:bg-primary-900 dark:text-primary-300"
                >
                    {{ $t('Successful') }}
                </span>
                <span
                    v-else
                    class="bg-red-100 text-red-800 text-sm font-medium mr-2 px-2.5 py-0.5 rounded dark:bg-red-900 dark:text-red-300"
                >
                    {{ $t('Failure') }}
                </span>
            </h3>
            <div v-if="props.amount_divide_100 && props.isSuccess" class="text-red-400 text-lg">
                - {{ props.amount_divide_100 }}$
            </div>
            <div v-else class="text-gray-400 text-lg">{{ props.amount_divide_100 }}$</div>
        </div>
        
        <!-- Extend Feature Benefits -->
        <div v-if="props.isSuccess" class="mt-3 mb-3">
            <div class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{{ $t('Benefits activated:') }}</div>
            <ul class="space-y-1 text-xs text-gray-600 dark:text-gray-400">
                <li class="flex items-center space-x-2">
                    <svg class="flex-shrink-0 w-3 h-3 text-purple-500" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span>{{ $t('Unlimited GPT-4o-mini usage for 1 year') }}</span>
                </li>
                <li class="flex items-center space-x-2">
                    <svg class="flex-shrink-0 w-3 h-3 text-purple-500" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span>{{ $t('Works even with 0 tokens') }}</span>
                </li>
                <li class="flex items-center space-x-2">
                    <svg class="flex-shrink-0 w-3 h-3 text-purple-500" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span>{{ $t('No credit lock for document translation') }}</span>
                </li>
            </ul>
        </div>

        <time class="block mb-2 text-sm font-normal leading-none text-gray-400 dark:text-gray-500">{{
            createdAtRawFormat
        }}</time>
        <p class="mb-1 text-sm font-normal text-gray-500 dark:text-gray-400">{{ $t('Order ID: ') }}{{ props.uuid }}</p>
        <p class="mb-4 text-sm font-normal text-gray-500 dark:text-gray-400">{{ $t('Paypal ID: ') }}{{ props.external_order_id }}</p>
        
        <!-- Validity Period -->
        <div v-if="props.isSuccess" class="mt-2 p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-800">
            <div class="flex items-center space-x-2">
                <svg class="w-4 h-4 text-purple-600 dark:text-purple-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                </svg>
                <span class="text-sm font-medium text-purple-800 dark:text-purple-200">
                    {{ $t('Valid until:') }} {{ validUntilDate }}
                </span>
            </div>
        </div>
    </li>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { storeToRefs } from 'pinia'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import relativeTime from 'dayjs/plugin/relativeTime'
import { usePaymentsStore } from '~/stores/payments'

const paymentsStore = usePaymentsStore()
const { tokenUnit } = storeToRefs(paymentsStore)

const { $abbreviatedUnit } = useNuxtApp()
dayjs.extend(utc)
dayjs.extend(relativeTime)

const props = defineProps<{
    id: number
    uuid: string
    user_id: string
    product_id: string
    status: string
    amount: number
    amount_divide_100: number
    quantity: number
    type: string
    payment_method: string
    created_at: string
    updated_at: string
    isSuccess?: boolean
    order_product: {
        id: string
        name: string
        type: string
    }
    external_order_id: string
}>()

const createdAtRawFormat = computed(() => dayjs.unix(dayjs.utc(props.created_at).unix()).format('YYYY-MM-DD HH:mm:ss'))

// Calculate validity period (1 year from purchase date)
const validUntilDate = computed(() => {
    const purchaseDate = dayjs.utc(props.created_at)
    const validUntil = purchaseDate.add(1, 'year')
    return validUntil.format('YYYY-MM-DD')
})
</script>
