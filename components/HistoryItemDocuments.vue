<template>
    <li class="mb-10 ml-8">
        <span
            class="absolute flex items-center justify-center w-8 h-8 bg-orange-100 rounded-full -left-4 ring-8 ring-gray-50 dark:ring-gray-900 dark:bg-orange-600"
        >
            <svg
                class="w-5 h-5 text-primary-800 dark:text-primary-300"
                aria-hidden="true"
                fill="none"
                stroke="currentColor"
                stroke-width="1.5"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
            >
                <path
                    d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                ></path>
            </svg>
        </span>
        <div
            class="px-4 pt-2 pb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-700 dark:border-gray-600"
        >
            <div class="items-center justify-between mb-4 sm:flex">
                <time
                    class="group cursor-pointer flex flex-inline space-x-2 items-center text-xs font-normal text-gray-400 sm:order-last sm:mb-0 justify-between sm:justify-start"
                >
                    <div class="group-hover:hidden">{{ createdAtFormat }}</div>
                    <div class="hidden group-hover:block">{{ createdAtRawFormat }}</div>
                    <a
                        v-if="props.status !== 1"
                        @click="emit('delete')"
                        class="inline-flex -mr-1 justify-center p-2 text-gray-500 rounded-full cursor-pointer dark:text-gray-400 dark:hover:text-white hover:text-gray-900 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer"
                    >
                        <svg
                            class="w-5 h-5"
                            aria-hidden="true"
                            fill="none"
                            stroke="currentColor"
                            stroke-width="1.5"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path d="M6 18L18 6M6 6l12 12" stroke-linecap="round" stroke-linejoin="round"></path>
                        </svg>
                    </a>
                </time>
                <div class="text-sm font-normal text-gray-500 lex dark:text-gray-300">
                    <a class="font-base text-gray-900 dark:text-white mr-2">
                        {{ $t('languages-list.' + props.origin_lang?.toLowerCase()) }} →
                        {{ $t('languages-list.' + props.target_lang?.toLowerCase()) }}
                    </a>
                    <span
                        class="bg-yellow-100 text-gray-800 text-xs font-normal mr-2 px-2.5 py-0.5 rounded dark:bg-yellow-600 dark:text-gray-300"
                    >
                        {{ $t('tokens', { value: props.used_token }) }}</span
                    >
                    <span
                        class="bg-green-100 text-gray-800 text-xs font-normal mr-2 px-2.5 py-0.5 rounded dark:bg-green-600 dark:text-gray-300"
                    >
                        {{ used_model || 'GPT-3.5' }}</span
                    >
                    <span
                        v-if="props.custom_prompt"
                        class="bg-teal-500 text-gray-100 text-xs font-normal mr-2 px-2.5 py-0.5 rounded dark:bg-teal-300 dark:text-gray-700"
                    >
                        {{ $t('Custom prompt') }}</span
                    >
                </div>
            </div>
            <div class="flex flex-wrap md:flex-inline justify-between items-center">
                <div
                    class="flex md:flex-1 mb-4 md:mb-0 flex-inline items-center pl-3 text-sm font-normal text-gray-500 dark:text-gray-300"
                >
                    <svg
                        class="w-4 min-w-[22px] h-4 mr-2"
                        aria-hidden="true"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="1.5"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                        ></path>
                    </svg>
                    <div class="break-all">{{ props.trans_input }}</div>
                </div>
                <div class="w-full md:w-auto text-right" v-if="props.status <= 2">
                    <a
                        @click="downloadFile"
                        :disabled="props.status === 1"
                        class="inline-flex items-center px-3 py-1.5 text-sm font-medium text-primary-900 bg-white border border-gray-200 rounded-lg hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:outline-none focus:ring-gray-200 focus:text-blue-700 dark:bg-gray-800 dark:text-primary-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700 dark:focus:ring-gray-700 cursor-pointer"
                        :class="{ 'opacity-50 cursor-not-allowed': props.status === 1 }"
                    >
                        <svg
                            v-if="props.status === 2"
                            class="w-4 h-4 mr-2"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                fill-rule="evenodd"
                                d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm5 6a1 1 0 10-2 0v3.586l-1.293-1.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V8z"
                                clip-rule="evenodd"
                            ></path>
                        </svg>
                        {{
                            props.status === 1
                                ? `${$t('Translating...')} (${props.status_percentage || 0}%)`
                                : $t('Download translation')
                        }}
                    </a>
                </div>
                <div class="w-full md:w-auto text-right" v-if="props.status === 3">
                    <div
                        @click="showError = !showError"
                        class="cursor-pointer inline-flex items-center px-3 py-1.5 text-sm font-medium text-primary-900 bg-white border border-gray-200 rounded-lg hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:outline-none focus:ring-gray-200 focus:text-blue-700 dark:bg-gray-800 dark:text-primary-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700 dark:focus:ring-gray-700"
                    >
                        <svg
                            class="flex-shrink-0 inline w-4 h-4 mr-3 fill-red-600 dark:fill-red-500"
                            aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                        >
                            <path
                                d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm0 16a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3Zm1-5.034V12a1 1 0 0 1-2 0v-1.418a1 1 0 0 1 1.038-.999 1.436 1.436 0 0 0 1.488-1.441 1.501 1.501 0 1 0-3-.116.986.986 0 0 1-1.037.961 1 1 0 0 1-.96-1.037A3.5 3.5 0 1 1 11 11.466Z"
                            />
                        </svg>
                        <span class="text-sm text-red-600 dark:text-red-500">{{ $t('Error') }}</span>
                    </div>
                </div>
            </div>
            <div
                v-if="props.custom_prompt"
                class="mt-4 p-3 text-sm font-normal text-teal-500 border border-teal-200 rounded-lg bg-teal-50 dark:bg-teal-900 dark:border-teal-500 dark:text-teal-300"
            >
                <div class="font-semibold">{{ $t('Custom prompt') }}:</div>
                {{ props.custom_prompt || 'Here is your custom prompt' }}
            </div>
            <div
                v-if="props.status === 3 && showError"
                class="mt-4 p-3 text-sm font-normal text-red-500 border border-red-200 rounded-lg bg-red-50 dark:bg-gray-800 dark:border-gray-500 dark:text-red-500"
            >
                <div class="font-semibold flex flex-row items-center justify-between">
                    <div>
                        {{ $t('Error') }}: <span class="font-normal">{{ props.error_message || 'System error!' }}</span>
                    </div>
                    <div v-if="props.error_message === 'NotEnoughToken'">
                        <button
                            @click="router.push({ path: '/profile/add-tokens' })"
                            type="button"
                            class="text-white bg-yellow-800 hover:bg-yellow-900 focus:ring-4 focus:outline-none focus:ring-yellow-300 font-medium rounded-lg text-xs px-3 py-1.5 mr-2 text-center inline-flex items-center dark:bg-yellow-300 dark:text-gray-800 dark:hover:bg-yellow-400 dark:focus:ring-yellow-800"
                        >
                            {{ $t('Add tokens') }}

                            <svg
                                aria-hidden="true"
                                class="ml-2 h-4 w-4"
                                fill="none"
                                stroke="currentColor"
                                stroke-width="1.5"
                                viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    d="M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                ></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
            <div class="shrink-0 mt-4 flex justify-end">
                <HistoryItemBugReport v-if="props.status === 3" :uuid="props.uuid" />
                <HistoryItemRate v-else :key="props.uuid" :uuid="props.uuid" :rating="props.rating" />
            </div>
        </div>
    </li>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import relativeTime from 'dayjs/plugin/relativeTime'
import HistoryItemRate from './HistoryItemRate.vue'
import HistoryItemBugReport from './HistoryItemBugReport.vue'
dayjs.extend(relativeTime)
dayjs.extend(utc)

const props = defineProps<{
    uuid: string
    trans_input: string
    trans_result: string
    custom_prompt: string
    created_at: string
    origin_lang: string
    target_lang: string
    used_token: number
    status: number
    used_model: string
    rating: string
    error_message: string
    status_percentage: number
}>()
const emit = defineEmits(['delete', 'download'])

const isShowFull = ref(false)
const showError = ref(true)
const toggleIsShowFull = () => {
    isShowFull.value = !isShowFull.value
}

const downloadFile = () => {
    if (props.status === 2) {
        emit('download')
    }
}

const createdAtFormat = computed(() => dayjs().to(dayjs.utc(props.created_at)))
const createdAtRawFormat = computed(() => dayjs.unix(dayjs.utc(props.created_at).unix()).format('YYYY-MM-DD HH:mm:ss'))

const router = useRouter()
</script>
