<template>
    <div class="relative px-4 py-2 pb-6 bg-white rounded-lg dark:bg-gray-700 h-full">
        <a
            v-if="inputText"
            @click="translateStore.clearInputAndResultText()"
            class="absolute right-1.5 top-1.5 cursor-pointer inline-flex justify-center p-2 text-gray-500 rounded-full dark:text-gray-400 dark:hover:text-white hover:text-gray-900 hover:bg-gray-100 dark:hover:bg-gray-600"
        >
            <svg
                class="w-6 h-6"
                aria-hidden="true"
                fill="none"
                stroke="currentColor"
                stroke-width="1.5"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
            >
                <path d="M6 18L18 6M6 6l12 12" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
        </a>
        <div class="flex flex-col h-full">
            <div class="grow">
                <textarea
                    id="text-translate-input"
                    ref="translateInput"
                    class="scrollbar-thin w-full md:px-4 resize-none overflow-y-auto md:h-full min-h-[210px] px-0 text-sm text-gray-900 bg-white border-0 dark:bg-gray-700 focus:ring-0 dark:text-white dark:placeholder-gray-500 placeholder-gray-300"
                    required
                    v-model="inputText"
                    maxlength="5000"
                >
                </textarea>
            </div>
            <div v-show="showCustomPrompt" class="grow-0 border-t dark:border-gray-600">
                <div class="relative">
                    <textarea
                        id="custom-prompt-input"
                        ref="customPromptInput"
                        class="customPromptInput scrollbar-thin w-full px-4 resize-none overflow-y-auto min-h-[220px] px-0 text-sm text-gray-900 bg-white dark:bg-gray-700 focus:ring-0 dark:text-gray-300 dark:placeholder-gray-500 placeholder-gray-300 dark:border-gray-600 dark:focus:border-primary-500 focus:outline-none focus:ring-0 focus:border-primary-600"
                        required
                        v-model="customPrompt"
                        maxlength="1000"
                        :placeholder="$t('Input your custom prompt here')"
                    >
                    </textarea>
                    <div id="suggest-custom-promts" class="my-2">
                        <SuggestPrompts />
                    </div>
                    <p class="ml-auto text-xs text-gray-500 dark:text-gray-400 py-2">
                        {{ $t('Caution: Use custom prompt may cause translation result to be inaccurate.') }}
                    </p>
                    <label
                        for="floating_outlined"
                        class="absolute text-sm text-gray-500 dark:text-gray-400 duration-300 transform -translate-y-4 scale-75 top-2 z-10 origin-[0] bg-white dark:bg-gray-700 px-2 peer-focus:px-2 peer-focus:text-primary-600 peer-focus:dark:text-primary-500 peer-placeholder-shown:scale-100 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:top-1/2 peer-focus:top-2 peer-focus:scale-75 peer-focus:-translate-y-4 left-1"
                        >{{ $t('Custom prompt') }}</label
                    >
                </div>
            </div>
        </div>
        <a
            id="btn-toggle-custom-prompt"
            @click="showCustomPrompt = !showCustomPrompt"
            class="absolute flex flex-inline items-center md:bottom-[2px] left-3 text-right text-sm font-thin text-primary-600 dark:text-primary-300 underline cursor-pointer"
        >
            <svg
                class="w-4 h-4 mr-2"
                v-if="showCustomPrompt"
                aria-hidden="true"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 20 20"
            >
                <path
                    stroke="currentColor"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="1.5"
                    d="m13 7-6 6m0-6 6 6m6-3a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                />
            </svg>
            <svg
                v-else
                class="w-4 h-4 mr-2"
                aria-hidden="true"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 20 20"
            >
                <path
                    stroke="currentColor"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="1.5"
                    d="M7.75 4H19M7.75 4a2.25 2.25 0 0 1-4.5 0m4.5 0a2.25 2.25 0 0 0-4.5 0M1 4h2.25m13.5 6H19m-2.25 0a2.25 2.25 0 0 1-4.5 0m4.5 0a2.25 2.25 0 0 0-4.5 0M1 10h11.25m-4.5 6H19M7.75 16a2.25 2.25 0 0 1-4.5 0m4.5 0a2.25 2.25 0 0 0-4.5 0M1 16h2.25"
                />
            </svg>

            {{ showCustomPrompt ? $t('Use default prompt') : $t('Use custom prompt') }}
        </a>
        <div class="absolute md:bottom-[2px] right-2 text-right text-xs font-thin dark:text-gray-300">
            {{ inputText?.length }} / 5000
        </div>
    </div>
</template>

<script setup lang="ts">
import { watch, ref, onMounted, nextTick } from 'vue'
import { storeToRefs } from 'pinia'
import { useTranslateStore } from '~/stores/translate'
import _ from 'lodash'
import SuggestPrompts from './SuggestPrompts.vue'
const translateStore = useTranslateStore()

const { inputText, customPrompt, showCustomPrompt, translateOptions } = storeToRefs(translateStore)
const translateInput = ref(null)
const customPromptInput = ref(null)

onMounted(() => {
    translateInput.value.focus()
})

watch(showCustomPrompt, (value) => {
    if (value) {
        nextTick(() => {
            customPromptInput.value.focus()
        })
        translateOptions.value = {
            translateDomain: '',
            translateTone: '',
            translateWritingStyle: '',
        }
    } else {
        translateInput.value?.focus()
    }
})

//watch inputText
// watch(inputText, (value) => {
//     translateStore.clearResultText()
// })
</script>
