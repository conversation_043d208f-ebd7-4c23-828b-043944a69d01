<template>
  <select
    :disabled="isUploading || showCustomPrompt"
    v-model="translateOptions.translateTone"
    class="text-xs font-medium border border-gray-300 dark:border-gray-600 bg-gray-300 dark:bg-gray-600 rounded-lg block w-full p-2.5 focus:ring-primary-500 dark:focus:ring-primary-500 dark:focus:border-primary-500"
    :class="
      isUploading || showCustomPrompt
        ? 'bg-gray-300 dark:bg-gray-600 text-gray-400 dark:text-gray-400 cursor-not-allowed'
        : 'text-gray-900 dark:text-white  hover:bg-gray-400 dark:hover:bg-gray-700'
    "
  >
    <option selected value="">{{ $t("Select tone") }}</option>
    <option v-for="{ value, displayText } in options" :key="value" :value="value">
      {{ $t(displayText) }}
    </option>
  </select>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import _ from "lodash";
import { useI18n } from "vue-i18n";
import { useTranslateStore } from "~/stores/translate";
import { storeToRefs } from "pinia";
const translateStore = useTranslateStore();
const { translateOptions, isUploading, showCustomPrompt } = storeToRefs(translateStore);

const { t, locale } = useI18n({ useScope: "global" });

const options = _.orderBy(
  [
    { value: "Authoritative", text: "Authoritative" },
    { value: "Clinical", text: "Clinical" },
    { value: "Cold", text: "Cold" },
    { value: "Confident", text: "Confident" },
    { value: "Cynical", text: "Cynical" },
    { value: "Emotional", text: "Emotional" },
    { value: "Empathetic", text: "Empathetic" },
    { value: "Formal", text: "Formal" },
    { value: "Friendly", text: "Friendly" },
    { value: "Humorous", text: "Humorous" },
    { value: "Informal", text: "Informal" },
    { value: "Ironic", text: "Ironic" },
    { value: "Optimistic", text: "Optimistic" },
    { value: "Pessimistic", text: "Pessimistic" },
    { value: "Playful", text: "Playful" },
    { value: "Sarcastic", text: "Sarcastic" },
    { value: "Serious", text: "Serious" },
    { value: "Sympathetic", text: "Sympathetic" },
    { value: "Tentative", text: "Tentative" },
    { value: "Warm", text: "Warm" },
  ].map((item) => {
    return {
      ...item,
      displayText: t(item.text),
    };
  }),
  "displayText"
);
</script>
