<template>
  <ul
    class="flex w-max mx-auto border border-gray-300 dark:border-gray-600 px-1 py-1 rounded-xl text-xs font-medium text-center text-gray-500 dark:text-gray-400"
  >
    <li class="mr-1">
      <TheTooltip>
        <a
          @click="changeAIModel('gpt-4o-mini')"
          :class="{
            'text-white bg-primary-600 active hover:text-gray-200 hover:bg-primary-700':
              chatGPTVersion == 'gpt-4o-mini' && !hasActiveExtendFeature,
            'text-white bg-gradient-to-r from-purple-600 to-blue-600 active hover:from-purple-700 hover:to-blue-700':
              chatGPTVersion == 'gpt-4o-mini' && hasActiveExtendFeature,
            'border-2 border-purple-400 bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900 dark:to-blue-900':
              chatGPTVersion !== 'gpt-4o-mini' && hasActiveExtendFeature,
          }"
          class="relative flex flex-inline items-center cursor-pointer px-1.5 py-1.5 rounded-lg hover:text-gray-400 hover:bg-gray-100 dark:hover:bg-primary-800 dark:hover:text-white"
        >
          <!-- Unlimited Badge -->
          <div
            v-if="hasActiveExtendFeature"
            class="absolute inline-flex items-center justify-center h-4 px-1 text-[6px] rounded-md font-bold text-white bg-gradient-to-r from-purple-500 to-blue-500 border border-white -top-2 -right-1 dark:border-gray-900 animate-pulse"
          >
            {{ $t("UNLIMITED") }}
          </div>

          <svg
            class="w-4 h-4 mr-1 group-hover:text-gray-500 dark:text-gray-50 dark:group-hover:text-gray-300"
            :class="{
              'text-purple-600 dark:text-purple-300': hasActiveExtendFeature && chatGPTVersion !== 'gpt-4o-mini'
            }"
            aria-hidden="true"
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            fill="none"
            viewBox="0 0 24 24"
          >
            <path
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 18.5A2.493 2.493 0 0 1 7.51 20H7.5a2.468 2.468 0 0 1-2.4-3.154 2.98 2.98 0 0 1-.85-5.274 2.468 2.468 0 0 1 .92-3.182 2.477 2.477 0 0 1 1.876-3.344 2.5 2.5 0 0 1 3.41-1.856A2.5 2.5 0 0 1 12 5.5m0 13v-13m0 13a2.493 2.493 0 0 0 4.49 1.5h.01a2.468 2.468 0 0 0 2.403-3.154 2.98 2.98 0 0 0 .847-5.274 2.468 2.468 0 0 0-.921-3.182 2.477 2.477 0 0 0-1.875-3.344A2.5 2.5 0 0 0 14.5 3 2.5 2.5 0 0 0 12 5.5m-8 5a2.5 2.5 0 0 1 3.48-2.3m-.28 8.551a3 3 0 0 1-2.953-5.185M20 10.5a2.5 2.5 0 0 0-3.481-2.3m.28 8.551a3 3 0 0 0 2.954-5.185"
            />
          </svg>
          <span
            :class="{
              'text-purple-700 dark:text-purple-200 font-semibold': hasActiveExtendFeature && chatGPTVersion !== 'gpt-4o-mini'
            }"
          >
            {{ $t("GPT-4o-Mini") }}
          </span>
        </a>
        <template #tooltip>
          <div class="flex flex-col space-y-2">
            <div class="max-w-sm">
              {{
                isGPT4Enabled
                  ? $t(
                      "GPT-4o-Mini is a smaller version of GPT-4o, optimized for speed and efficiency."
                    )
                  : $t("Coming soon")
              }}
              <br />
              <div class="mt-2">
                <span>
                  {{ $t("For text in English: ") }}
                </span>
                <span>
                  {{ $t("1,000 words ~ 3,000 tokens.") }}
                </span>
              </div>

              <!-- Extend Feature Info -->
              <div v-if="hasActiveExtendFeature" class="mt-3 p-2 bg-gradient-to-r from-purple-100 to-blue-100 dark:from-purple-900 dark:to-blue-900 rounded-lg border border-purple-300 dark:border-purple-700">
                <div class="flex items-center space-x-1 mb-1">
                  <svg class="w-3 h-3 text-purple-600 dark:text-purple-300" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM10 15a1 1 0 1 1 0-2 1 1 0 0 1 0 2Zm1-4a1 1 0 0 1-2 0V6a1 1 0 0 1 2 0v5Z"/>
                  </svg>
                  <span class="text-xs font-semibold text-purple-700 dark:text-purple-200">{{ $t("UNLIMITED ACCESS") }}</span>
                </div>
                <div class="text-xs text-purple-600 dark:text-purple-300">
                  {{ $t("You have unlimited access to this model") }}
                </div>
                <div class="text-xs text-purple-500 dark:text-purple-400 mt-1">
                  {{ $t("Valid until") }}: {{ extendFeatureExpireDate }}
                </div>
              </div>

              <div v-else class="mt-2 text-yellow-300">
                {{
                  !usableModelList.includes("gpt-4o-mini")
                    ? $t("Update your plan to use this model.")
                    : ""
                }}
              </div>
            </div>
          </div>
        </template>
      </TheTooltip>
    </li>
    <li class="mr-1">
      <TheTooltip>
        <a
          @click="changeAIModel('gpt-4')"
          :class="{
            'text-white bg-primary-600 active hover:text-gray-200 hover:bg-primary-700':
              chatGPTVersion == 'gpt-4',
          }"
          class="flex flex-inline items-center cursor-pointer px-1.5 py-1.5 rounded-lg hover:text-gray-400 hover:bg-gray-100 dark:hover:bg-primary-800 dark:hover:text-white"
        >
          <svg
            class="w-4 h-4 mr-1 group-hover:text-gray-500 dark:text-gray-50 dark:group-hover:text-gray-300"
            aria-hidden="true"
            xmlns="http://www.w3.org/2000/svg"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              d="m7.164 3.805-4.475.38L.327 6.546a1.114 1.114 0 0 0 .63 1.89l3.2.375 3.007-5.006ZM11.092 15.9l.472 3.14a1.114 1.114 0 0 0 1.89.63l2.36-2.362.38-4.475-5.102 3.067Zm8.617-14.283A1.613 1.613 0 0 0 18.383.291c-1.913-.33-5.811-.736-7.556 1.01-1.98 1.98-6.172 9.491-7.477 11.869a1.1 1.1 0 0 0 .193 1.316l.986.985.985.986a1.1 1.1 0 0 0 1.316.193c2.378-1.3 9.889-5.5 11.869-7.477 1.746-1.745 1.34-5.643 1.01-7.556Zm-3.873 6.268a2.63 2.63 0 1 1-3.72-3.72 2.63 2.63 0 0 1 3.72 3.72Z"
            />
          </svg>

          {{ $t("GPT-4") }}
        </a>
        <template #tooltip>
          <div class="flex flex-col space-y-2">
            <div class="max-w-sm">
              {{
                isGPT4Enabled
                  ? $t(
                      "Our most capable model, great for tasks that require creativity and advanced reasoning."
                    )
                  : $t("Coming soon")
              }}
              <br />
              <div class="mt-2">
                <span>
                  {{ $t("For text in English: ") }}
                </span>
                <span>
                  {{ $t("1,000 words ~ 90,000 tokens") }}
                </span>
              </div>
              <div class="mt-2 text-yellow-300">
                {{
                  !usableModelList.includes("gpt-4")
                    ? $t("Update your plan to use this model.")
                    : ""
                }}
              </div>
            </div>
          </div>
        </template>
      </TheTooltip>
    </li>

    <li v-if="!['gpt-4-turbo', 'gpt-3.5'].includes(chatGPTVersion)">
      <TheTooltip>
        <a
          @click="changeAIModel('gpt-4o')"
          :class="{
            'text-white bg-primary-600 active hover:text-gray-200 hover:bg-primary-700':
              chatGPTVersion == 'gpt-4o',
          }"
          class="relative flex flex-inline items-center cursor-pointer px-1.5 py-1.5 rounded-lg hover:text-gray-400 hover:bg-gray-100 dark:hover:bg-primary-800 dark:hover:text-white"
        >
          <svg
            class="w-4 h-4 mr-1 group-hover:text-gray-500 dark:text-gray-50 dark:group-hover:text-gray-300"
            aria-hidden="true"
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            fill="none"
            viewBox="0 0 24 24"
          >
            <path
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M8 8v8m0-8h8M8 8H6a2 2 0 1 1 2-2v2Zm0 8h8m-8 0H6a2 2 0 1 0 2 2v-2Zm8 0V8m0 8h2a2 2 0 1 1-2 2v-2Zm0-8h2a2 2 0 1 0-2-2v2Z"
            />
          </svg>
          <div
            v-if="chatGPTVersion !== 'gpt-4o'"
            class="absolute inline-flex items-center justify-center h-5 px-1 text-[7px] rounded-md font-bold text-white bg-red-500 border-2 border-white -top-4 left-5 dark:border-gray-900 animate__animated animate__tada animate__infinite animate__slower"
          >
            {{ $t("New !") }}
          </div>
          <span>{{ $t("GPT-4o") }}</span>
        </a>
        <template #tooltip>
          <div class="flex flex-col space-y-2">
            <div class="max-w-sm">
              {{
                isGPT4Enabled
                  ? $t(
                      "GPT-4o has the best vision and performance across non-English languages of any of our models."
                    )
                  : $t("Coming soon")
              }}
              <br />
              <div class="mt-2">
                <span>
                  {{ $t("For text in English: ") }}
                </span>
                <span>
                  {{ $t("1,000 words ~ 30,000 tokens.") }}
                </span>
              </div>
              <div class="mt-2 text-yellow-300">
                {{
                  !usableModelList.includes("gpt-4o")
                    ? $t("Update your plan to use this model.")
                    : ""
                }}
              </div>
            </div>
          </div>
        </template>
      </TheTooltip>
    </li>

    <!-- <div class="pl-2">
      <VersionMenuExtend />
    </div> -->
  </ul>
</template>

<script lang="ts" setup>
import { computed } from "vue";
import { storeToRefs } from "pinia";
import { useAppStore } from "~~/stores/app";
import { useAuthStore } from "~~/stores/auth";
import TheTooltip from "~/base-components/TheTooltip.vue";
import { usePaymentsStore } from "~~/stores/payments";
import dayjs from 'dayjs';

const paymentStore = usePaymentsStore();
const authStore = useAuthStore();

const { usableModelList } = storeToRefs(paymentStore);
const { user } = storeToRefs(authStore);
const appStore = useAppStore();

const { chatGPTVersion } = storeToRefs(appStore);

const config = useRuntimeConfig();
const isGPT4Enabled = config.public.NUXT_MODEL_GPT_4;

// Check if user has active extend feature (BP0001)
const hasActiveExtendFeature = computed(() => {
  if (!user.value?.user_benefits) return false

  const extendFeatureBenefit = user.value.user_benefits.find(
    benefit => benefit.product.id === 'BP0001'
  )

  if (!extendFeatureBenefit) return false

  // Check if not expired
  const now = dayjs()
  const expireDate = dayjs(extendFeatureBenefit.expire_at)

  return expireDate.isAfter(now)
})

// Get extend feature expire date
const extendFeatureExpireDate = computed(() => {
  if (!user.value?.user_benefits) return ''

  const extendFeatureBenefit = user.value.user_benefits.find(
    benefit => benefit.product.id === 'BP0001'
  )

  if (!extendFeatureBenefit) return ''

  return dayjs(extendFeatureBenefit.expire_at).format('YYYY-MM-DD')
})

const changeAIModel = (model: string) => {
  // Allow gpt-4o-mini if user has extend feature, regardless of plan
  if (model === 'gpt-4o-mini' && hasActiveExtendFeature.value) {
    appStore.changeChatGPTVersion(model);
    return;
  }

  if (usableModelList.value.includes(model)) {
    appStore.changeChatGPTVersion(model);
  } else {
    navigateTo("/pricing-plans");
  }
};
</script>
