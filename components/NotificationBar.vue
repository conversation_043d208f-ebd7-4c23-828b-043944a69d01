<template>
    <button
        id="dropdownNotificationButton"
        data-dropdown-toggle="dropdownNotification"
        class="inline-flex items-center text-sm font-medium text-center text-gray-500 hover:text-gray-900 focus:outline-none dark:hover:text-white dark:text-gray-400"
        type="button"
    >
        <svg
            class="w-8 h-8"
            aria-hidden="true"
            fill="currentColor"
            viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z"
            ></path>
        </svg>
        <div v-if="hasUnreadNotifications" class="relative flex">
            <div
                class="relative inline-flex w-3 h-3 bg-red-500 border-2 border-white rounded-full -top-2 right-3 dark:border-gray-900"
            ></div>
        </div>
    </button>
    <!-- Dropdown menu -->
    <div
        id="dropdownNotification"
        class="z-40 hidden w-full max-w-sm bg-white divide-y divide-gray-100 rounded-lg shadow-md dark:bg-gray-700 dark:divide-gray-600"
        aria-labelledby="dropdownNotificationButton"
    >
        <div
            class="block px-4 py-2 font-medium text-center text-gray-700 rounded-t-lg bg-white dark:bg-gray-700 dark:text-white"
        >
            {{ $t('Notifications') }}
        </div>
        <div
            id="notification-rows"
            class="scrollbar scrollbar-thin divide-y divide-gray-100 dark:divide-gray-700 max-h-[400px] overflow-y-auto"
        >
            <NotificationRow
                v-show="sortedNotifications && sortedNotifications[0]?.uuid === 'fake-notification-uuid'"
                id="fake-notification-uuid"
                :seen="false"
                fileName="your_file.pdf"
                :createdAt="new Date().toISOString()"
                translationId="fake-notification-uuid"
                historyId="fake-notification-uuid"
                :success="true"
            />
            <NotificationRow
                :id="notify.uuid"
                v-for="notify in sortedNotifications"
                :seen="notify.seen"
                :fileName="notify.document_name"
                :createdAt="notify.created_at"
                :translationId="notify.translation_history_id"
                :historyId="notify.uuid"
                :success="notify.success"
                @onNotificationDetail="onNotificationDetail(notify.uuid)"
                @onDownloadFile="onDownloadFile(notify)"
            />
            <div v-if="showAds">
                <div data-type="_mgwidget" data-widget-id="1602685"></div>
            </div>
        </div>
        <div
            v-if="sortedNotifications.length === 0 || Object.keys(sortedNotifications).length === 0"
            class="block p-5 font-thin text-sm text-center text-gray-700 dark:text-white"
        >
            <svg
                class="w-10 h-10 text-gray-800 dark:text-white mx-auto mb-4"
                aria-hidden="true"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 20 20"
            >
                <path
                    stroke="currentColor"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="1.5"
                    d="M5 4a4 4 0 0 1 4 4v6M5 4a4 4 0 0 0-4 4v6h8M5 4h9M9 14h10V8a3.999 3.999 0 0 0-2.066-3.5M9 14v5m0-5h4v5m-9-8h2m8-4V1h2"
                />
            </svg>
            {{ $t('No notifications') }}
        </div>
        <a
            @click="markAllAsRead"
            class="block py-2 text-sm font-medium text-center text-gray-900 rounded-b-lg bg-gray-50 hover:bg-gray-100 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-white cursor-pointer"
        >
            <div class="inline-flex items-center">
                <svg
                    class="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400"
                    aria-hidden="true"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                    <path
                        fill-rule="evenodd"
                        d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                        clip-rule="evenodd"
                    ></path>
                </svg>
                {{ $t('Mark all as read') }}
            </div>
        </a>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { Dropdown } from 'flowbite'
import NotificationRow from './NotificationRow.vue'
import { useNotificationsStore } from '@/stores/notifications'
import { useAppStore } from '@/stores/app'
import { storeToRefs } from 'pinia'
import { useAuthStore } from '~~/stores/auth'

const appStore = useAppStore()
const authStore = useAuthStore()
const { showAds } = storeToRefs(authStore)
const notificationsStore = useNotificationsStore()
const { notifications, hasUnreadNotifications, sortedNotifications } = storeToRefs(notificationsStore)
const { showNotificationsDropdown } = storeToRefs(appStore)
let dropdown
const router = useRouter()
onMounted(() => {
    if (process.client) {
        const $dropdownLangEl = document.getElementById('dropdownNotification')
        const $triggerDropdownLangEl = document.getElementById('dropdownNotificationButton')

        // options with default values
        const options = {
            placement: 'bottom',
            triggerType: 'click',
            // offsetSkidding: 0,
            // offsetDistance: 10,
            delay: 100,
            onHide: () => {
                appStore.setShowNotificationsDropdown(false)
            },
            onShow: () => {
                appStore.setShowNotificationsDropdown(true)
            },
            onToggle: () => {
                console.log('dropdown has been toggled')
            },
        }

        dropdown = new Dropdown($dropdownLangEl, $triggerDropdownLangEl, options)

        nextTick(() => {
            let script = document.createElement('script')
            script.innerHTML = `(function(w,q){w[q]=w[q]||[];w[q].push(["_mgc.load"])})(
            window,"_mgq"
            );`
            document.body.appendChild(script)
        })
    }
})
watch(
    () => showNotificationsDropdown.value,
    (value) => {
        if (value) {
            dropdown.show()
        } else {
            dropdown.hide()
        }
    }
)

const onNotificationDetail = (id) => {
    notificationsStore.markAsRead(id)
    dropdown.hide()
    router.push({ name: 'history', query: { id } })
}

const onDownloadFile = (notify) => {
    notificationsStore.markAsRead(notify.uuid)
    // dropdown.hide()
    notificationsStore.downloadFile(notify.translation_history_id)
}

const markAllAsRead = () => {
    notificationsStore.markAllAsRead()
    // dropdown.hide()
}
</script>
