<template>
    <ul
        class="flex w-max mx-auto border border-dashed border-gray-300 dark:border-gray-600 px-3 py-2 rounded-xl text-sm font-medium text-center text-gray-500 dark:text-gray-400"
    >
        <li class="mr-2">
            <a
                class="flex flex-inline items-center cursor-pointer px-4 py-3 rounded-lg hover:text-gray-900 hover:bg-gray-100 dark:hover:bg-gray-800 dark:hover:text-white"
                :class="{
                    'text-white bg-primary-600 active hover:text-gray-200 hover:bg-primary-700': filterBy == 'all',
                }"
                @click="setFilterBy('all')"
                aria-current="page"
            >
                <svg
                    class="w-5 h-5 mr-2 group-hover:text-gray-500 dark:text-gray-50 dark:group-hover:text-gray-300"
                    aria-hidden="true"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="1.5"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path
                        d="M5.25 8.25h15m-16.5 7.5h15m-1.8-13.5l-3.9 19.5m-2.1-19.5l-3.9 19.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                    ></path>
                </svg>

                {{ $t('All') }}
            </a>
        </li>
        <li class="mr-2">
            <a
                :class="{
                    'text-white bg-primary-600 active hover:text-gray-200 hover:bg-primary-700':
                        filterBy == 'translate-text',
                }"
                @click="setFilterBy('translate-text')"
                class="flex flex-inline items-center cursor-pointer px-4 py-3 rounded-lg hover:text-gray-900 hover:bg-gray-100 dark:hover:bg-gray-800 dark:hover:text-white"
            >
                <svg
                    class="w-5 h-5 mr-2 group-hover:text-gray-500 dark:text-gray-50 dark:group-hover:text-gray-300"
                    aria-hidden="true"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="1.5"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path
                        d="M10.5 21l5.25-11.25L21 21m-9-3h7.5M3 5.621a48.474 48.474 0 016-.371m0 0c1.12 0 2.233.038 3.334.114M9 5.25V3m3.334 2.364C11.176 10.658 7.69 15.08 3 17.502m9.334-12.138c.896.061 1.785.147 2.666.257m-4.589 8.495a18.023 18.023 0 01-3.827-5.802"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                    ></path>
                </svg>

                {{ $t('Text') }}
            </a>
        </li>
        <li class="mr-0">
            <a
                :class="{
                    'text-white bg-primary-600 active hover:text-gray-200 hover:bg-primary-700':
                        filterBy == 'translate-document',
                }"
                @click="setFilterBy('translate-document')"
                class="flex flex-inline items-center cursor-pointer px-4 py-3 rounded-lg hover:text-gray-900 hover:bg-gray-100 dark:hover:bg-gray-800 dark:hover:text-white"
            >
                <svg
                    class="w-5 h-5 mr-2 group-hover:text-gray-500 dark:text-gray-50 dark:group-hover:text-gray-300"
                    aria-hidden="true"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="1.5"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path
                        d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                    ></path>
                </svg>
                {{ $t('Documents') }}
            </a>
        </li>
    </ul>
</template>

<script lang="ts" setup>
import { watch } from 'vue'
import { storeToRefs } from 'pinia'
import { useHistoryStore } from '~~/stores/history'

const historyStore = useHistoryStore()
const route = useRoute()
const router = useRouter()

const { filterBy, currentPage } = storeToRefs(historyStore)

const setFilterBy = (filter: string) => {
    router.push({
        query: {
            filterBy: filter,
        },
    })
    historyStore.setFilterBy(filter)
}

watch(filterBy, async () => {
    // Reset the page to 1 everytime filter changes
    currentPage.value = 1
    await historyStore.filterHistories()
})

watch(
    () => route.query,
    (value) => {
        if (value?.filterBy) {
            historyStore.setFilterBy(value.filterBy as string)
        } else if(!value?.id) {
            historyStore.setFilterBy('all')
        }
    },
    {
        immediate: true,
        deep: true,
    }
)
</script>
