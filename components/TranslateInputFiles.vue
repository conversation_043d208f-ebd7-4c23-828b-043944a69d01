<template>
    <div class="relative bg-white rounded-lg dark:bg-gray-700 h-full">
        <a
            v-if="inputFile && !isUploading"
            @click="translateStore.clearInputFile()"
            class="absolute right-1.5 top-1.5 cursor-pointer inline-flex justify-center p-2 text-gray-500 rounded-full dark:text-gray-400 dark:hover:text-white hover:text-gray-900 hover:bg-gray-100 dark:hover:bg-gray-600"
        >
            <svg
                class="w-6 h-6"
                aria-hidden="true"
                fill="none"
                stroke="currentColor"
                stroke-width="1.5"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
            >
                <path d="M6 18L18 6M6 6l12 12" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
        </a>
        <div class="flex items-center justify-center w-full">
            <label
                id="dropzone-file-input"
                @drop.prevent="dropHandler"
                @dragover="dragOverHandler"
                for="dropzone-file"
                class="flex flex-col items-center rounded-lg justify-center w-full h-64 cursor-pointer bg-white dark:hover:bg-bray-800 dark:bg-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:hover:border-gray-500 dark:hover:bg-gray-600"
            >
                <div class="relative" v-show="inputFile">
                    <a
                        href="#"
                        class="relative flex md:min-w-[330px] flex-inline space-x-3 max-w-sm md:max-w-sm px-4 py-2 bg-gray-100 rounded-lg hover:bg-gray-100 dark:bg-gray-800 dark:border-gray-700 dark:hover:bg-gray-700"
                        :class="
                            isInvalidFile
                                ? 'border-2 border-yellow-500 dark:border-yellow-300'
                                : documentResult === 'ERROR'
                                ? 'border-2 border-red-500 dark:border-red-500'
                                : ''
                        "
                    >
                        <div
                            v-if="isInvalidFile"
                            class="absolute inline-flex items-center justify-center w-6 h-6 text-xs font-bold text-white bg-yellow-500 dark:bg-yellow-300 border-2 border-white rounded-full -top-2 -right-2 dark:border-gray-900"
                        >
                            <svg
                                class="w-2.5 h-2.5"
                                aria-hidden="true"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 14 14"
                            >
                                <path
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                    d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"
                                />
                            </svg>
                        </div>
                        <div>
                            <svg
                                class="w-10 h-10 mb-3 text-gray-400"
                                aria-hidden="true"
                                fill="none"
                                stroke="currentColor"
                                stroke-width="1.5"
                                viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                ></path>
                            </svg>
                        </div>
                        <div class="truncate">
                            <p class="font-normal text-gray-700 dark:text-gray-400 truncate">
                                {{ inputFile?.name }}
                            </p>
                            <p class="text-sm font-thin text-gray-700 dark:text-gray-400">
                                {{ $fileSizeFormat(inputFile?.size) }}
                            </p>
                        </div>
                    </a>

                    <div v-if="isInvalidFile" class="flex flex-col text-yellow-500 dark:text-yellow-300 mt-2">
                        <small>{{ $t('Invalid file format.') }}</small>
                        <small>{{ $t('Please convert to docx, pptx or xlsx before translating') }}</small>
                    </div>

                    <div
                        v-if="!uploadProgress && documentResult && documentResult === 'STARTED'"
                        class="absolute inline-flex items-center justify-center w-6 h-6 text-xs font-bold text-white bg-green-500 border-2 border-white rounded-full -top-2 -right-2 dark:border-gray-900"
                    >
                        <svg
                            class="w-2.5 h-2.5"
                            aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 16 12"
                        >
                            <path
                                stroke="currentColor"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M1 5.917 5.724 10.5 15 1.5"
                            />
                        </svg>
                    </div>
                    <div
                        v-if="uploadProgress"
                        class="absolute top-0 h-full bg-gray-500 dark:bg-gray-300 rounded-lg opacity-5 text-center"
                        :style="`width: ${uploadProgress}%`"
                    ></div>
                </div>
                <div
                    v-show="inputFile && documentResult !== 'STARTED' && !isFileProtected && !isTranslating"
                    class="flex items-center mt-2"
                >
                    <input
                        id="default-checkbox"
                        type="checkbox"
                        v-model="isFileHasPassword"
                        class="w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 rounded focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                    />
                    <label for="default-checkbox" class="ml-2 text-sm font-medium text-gray-900 dark:text-gray-300">{{
                        $t('Is the file password protected?')
                    }}</label>
                </div>
                <div v-show="!inputFile" class="flex flex-col items-center justify-center pt-5 pb-6">
                    <svg
                        aria-hidden="true"
                        class="w-10 h-10 mb-3 text-gray-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                        ></path>
                    </svg>
                    <p class="mb-2 text-sm text-gray-500 dark:text-gray-400 text-center">
                        <span class="font-semibold">
                            {{ $t('Click to upload') }}
                        </span>
                        <br />
                        {{ $t('or drag and drop your file here') }}
                    </p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">
                        {{ $t('docx, pptx, xlsx, pdf (MAX. 100MB)') }}
                    </p>
                </div>
                <div class="text-sm mt-2 text-gray-500 dark:text-gray-400">
                    <p
                        v-if="
                            documentResult !== 'STARTED' &&
                            (isFileProtected || (isFileHasPassword && inputFile && !isTranslating))
                        "
                        class="text-red-500 mb-3"
                    >
                        <input
                            ref="filePasswordInput"
                            type="password"
                            id="password"
                            v-model="filePassword"
                            class="mt-2 md:min-w-[330px] bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-800 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                            :placeholder="$t('Enter file password here')"
                            required
                        />
                        <div v-if="documentResult === 'ERROR'" class="mt-2">
                            {{ $t('Error') }}: {{ $t(translateError) }}
                        </div>
                    </p>
                    <p v-else-if="documentResult === 'ERROR'" class="text-red-500">
                        {{ $t('Error') }}: {{ $t(translateError) }}
                    </p>
                    <p v-if="uploadProgress && uploadProgress < 100" class="font-medium animate-pulse">
                        {{ $t('Upload progress') }}: {{ uploadProgress }}%
                    </p>
                    <p
                        v-if="uploadProgress && uploadProgress === 100 && documentResult !== 'ERROR'"
                        class="font-medium animate-pulse"
                    >
                        {{ $t('File upload completed! Starting to translate...') }}
                    </p>
                    <p v-if="!uploadProgress && documentResult && documentResult === 'STARTED'">
                        {{ $t('File translation started!') }}
                    </p>
                </div>
                <input
                    :value="fileInput"
                    id="dropzone-file"
                    type="file"
                    class="hidden"
                    @change="handleFileUpload"
                    :disabled="isUploading"
                    accept="application/pdf, .docx, .doc, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel, application/vnd.ms-powerpoint, application/vnd.openxmlformats-officedocument.presentationml.presentation"
                />
            </label>
        </div>

        <div v-show="showCustomPrompt" class="grow-0 px-4 pb-6 border-t dark:border-gray-600">
            <div class="relative">
                <textarea
                    id="custom-prompt-input"
                    ref="customPromptInput"
                    class="scrollbar-thin w-full px-4 resize-none overflow-y-auto min-h-[180px] px-0 text-sm text-gray-900 bg-white dark:bg-gray-700 focus:ring-0 dark:text-gray-300 dark:placeholder-gray-500 placeholder-gray-300 dark:border-gray-600 dark:focus:border-primary-500 focus:outline-none focus:ring-0 focus:border-primary-600"
                    required
                    v-model="customPrompt"
                    maxlength="1000"
                    :placeholder="$t('Input your custom prompt here')"
                >
                </textarea>
                <div id="suggest-custom-promts" class="my-2">
                    <SuggestPrompts />
                </div>
                <p class="ml-auto text-xs text-gray-500 dark:text-gray-400 py-2">
                    {{ $t('Caution: Use custom prompt may cause translation result to be inaccurate.') }}
                </p>
                <label
                    for="floating_outlined"
                    class="absolute text-sm text-gray-500 dark:text-gray-400 duration-300 transform -translate-y-4 scale-75 top-2 z-10 origin-[0] bg-white dark:bg-gray-700 px-2 peer-focus:px-2 peer-focus:text-primary-600 peer-focus:dark:text-primary-500 peer-placeholder-shown:scale-100 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:top-1/2 peer-focus:top-2 peer-focus:scale-75 peer-focus:-translate-y-4 left-1"
                    >{{ $t('Custom prompt') }}</label
                >
            </div>
        </div>
        <a
            id="btn-toggle-custom-prompt"
            @click="showCustomPrompt = !showCustomPrompt"
            class="absolute flex flex-inline items-center bottom-[3px] md:bottom-[2px] left-3 text-right text-sm font-thin text-primary-600 dark:text-primary-300 underline cursor-pointer"
        >
            <svg
                class="w-4 h-4 mr-2"
                v-if="showCustomPrompt"
                aria-hidden="true"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 20 20"
            >
                <path
                    stroke="currentColor"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="1.5"
                    d="m13 7-6 6m0-6 6 6m6-3a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                />
            </svg>
            <svg
                v-else
                class="w-4 h-4 mr-2"
                aria-hidden="true"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 20 20"
            >
                <path
                    stroke="currentColor"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="1.5"
                    d="M7.75 4H19M7.75 4a2.25 2.25 0 0 1-4.5 0m4.5 0a2.25 2.25 0 0 0-4.5 0M1 4h2.25m13.5 6H19m-2.25 0a2.25 2.25 0 0 1-4.5 0m4.5 0a2.25 2.25 0 0 0-4.5 0M1 10h11.25m-4.5 6H19M7.75 16a2.25 2.25 0 0 1-4.5 0m4.5 0a2.25 2.25 0 0 0-4.5 0M1 16h2.25"
                />
            </svg>

            {{ showCustomPrompt ? $t('Use default prompt') : $t('Use custom prompt') }}
        </a>
    </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue'
import { storeToRefs } from 'pinia'
import { useTranslateStore } from '~/stores/translate'
import SuggestPrompts from './SuggestPrompts.vue'
const translateStore = useTranslateStore()
const { $fileSizeFormat } = useNuxtApp()
const {
    inputFile,
    isUploading,
    showCustomPrompt,
    customPrompt,
    translateOptions,
    uploadProgress,
    documentResult,
    isInvalidFile,
    translateError,
    isFileProtected,
    filePassword,
    isFileHasPassword,
    isTranslating,
} = storeToRefs(translateStore)

const customPromptInput = ref(null)

const fileInput = ref(null)

const filePasswordInput = ref(null)

const handleFileUpload = (e: Event) => {
    const files = (e.target as HTMLInputElement).files
    if (files?.length > 0) {
        translateStore.clearInputFile()
        const file = files[0]
        translateStore.setInputFile(file)
        translateStore.setMode('document')
    }
}

const dragOverHandler = (e: Event) => {
    e.preventDefault()
}

const dropHandler = (e: Event) => {
    const files = e.dataTransfer.files
    if (files && files.length > 0) {
        const file = files[0]
        translateStore.clearInputFile()
        translateStore.setInputFile(file)
        translateStore.setMode('document')
    }
}

watch(showCustomPrompt, (value) => {
    if (value) {
        nextTick(() => {
            customPromptInput.value.focus()
        })
        translateOptions.value = {
            translateDomain: '',
            translateTone: '',
            translateWritingStyle: '',
        }
    }
})

watch(isFileProtected, (value) => {
    if (value) {
        nextTick(() => {
            filePasswordInput.value.focus()
        })
    }
})
</script>
