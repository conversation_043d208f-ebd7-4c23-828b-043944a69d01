// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
    ssr: false,
    components: true,
    devtools: { enabled: true },
    modules: ['@nuxtjs/tailwindcss', '@pinia/nuxt'],
    build: { transpile: ['vue-number-animation'] },
    buildModules: ['@nuxtjs/pwa'],
    tailwindcss: {
        cssPath: '~/assets/css/input.css',
    },
    app: {
        head: {
            title: 'DocTransGPT | Professional business translator',
            viewport: 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no',
            script: [
                {
                    src: 'https://cdn.jsdelivr.net/npm/tsparticles@1.18.3/dist/tsparticles.min.js',
                },
                // {
                //     children: `
                //         !function(f,b,e,v,n,t,s)
                //         {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
                //         n.callMethod.apply(n,arguments):n.queue.push(arguments)};
                //         if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
                //         n.queue=[];t=b.createElement(e);t.async=!0;
                //         t.src=v;s=b.getElementsByTagName(e)[0];
                //         s.parentNode.insertBefore(t,s)}(window, document,'script',
                //         'https://connect.facebook.net/en_US/fbevents.js');
                //         fbq('init', ${process.env.NUXT_FACEBOOK_PIXEL_ID || '260484196919513'});
                //         fbq('track', 'PageView');
                //     `,
                // },
                {
                    children: `(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
                    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
                    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
                    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
                    })(window,document,'script','dataLayer','${process.env.NUXT_GOOGLE_ANALYTICS_ID}');`,
                    tagPosition: 'head',
                },
                {
                    src: 'https://jsc.adskeeper.co.uk/site/949285.js',
                    async: true,
                },
                {
                    children: `(function(w,q){w[q]=w[q]||[];w[q].push(["_mgc.load"])})(window,"_mgq");`,
                    body: true,
                },
            ],
            noscript: [
                // {
                //     innerHTML: `<img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=${
                //         process.env.NUXT_FACEBOOK_PIXEL_ID || '260484196919513'
                //     }&ev=PageView&noscript=1" />`,
                // },
                {
                    innerHTML: `<iframe src="https://www.googletagmanager.com/ns.html?id=${process.env.NUXT_GOOGLE_ANALYTICS_ID}"
                    height="0" width="0" style="display:none;visibility:hidden"></iframe>`,
                    tagPosition: 'bodyOpen',
                },
                // {
                //     innerHTML: `<div data-type="_mgwidget" data-widget-id="1602639"> </div>`,
                //     tagPosition: 'bodyOpen',
                // },
            ],

            link: [
                {
                    rel: 'stylesheet',
                    href: 'https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200',
                },
                {
                    rel: 'stylesheet',
                    href: 'https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css',
                },

                // <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
                // <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
                // <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
                // <link rel="manifest" href="/site.webmanifest">
                // <link rel="mask-icon" href="/safari-pinned-tab.svg" color="#5bbad5">

                {
                    rel: 'apple-touch-icon',
                    sizes: '180x180',
                    href: '/apple-touch-icon.png',
                },
                {
                    rel: 'icon',
                    type: 'image/png',
                    sizes: '32x32',
                    href: '/favicon-32x32.png',
                },
                {
                    rel: 'icon',
                    type: 'image/png',
                    sizes: '16x16',
                    href: '/favicon-16x16.png',
                },
                {
                    rel: 'manifest',
                    href: '/site.webmanifest',
                },
                {
                    rel: 'mask-icon',
                    href: '/safari-pinned-tab.svg',
                    color: '#5bbad5',
                },
            ],
            // <meta name="msapplication-TileColor" content="#da532c">
            // <meta name="theme-color" content="#ffffff">
            meta: [
                {
                    name: 'msapplication-TileColor',
                    content: '#da532c',
                },
                {
                    name: 'theme-color',
                    content: '#ffffff',
                },
            ],
            // <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />
        },
    },
    runtimeConfig: {
        public: {
            baseURL: process.env.NUXT_API_BASE_URL || 'https://api-dev.doctransgpt.com/api/v1/',
            // noStreamTranslate: process.env.NUXT_NO_STREAM_TRANSLATION || false,
            NUXT_FIREBASE_API_KEY: process.env.NUXT_FIREBASE_API_KEY || '',
            NUXT_FIREBASE_AUTH_DOMAIN: process.env.NUXT_FIREBASE_AUTH_DOMAIN || '',
            NUXT_FIREBASE_DATABASE_URL: process.env.NUXT_FIREBASE_DATABASE_URL || '',
            NUXT_FIREBASE_PROJECT_ID: process.env.NUXT_FIREBASE_PROJECT_ID || '',
            NUXT_FIREBASE_STORAGE_BUCKET: process.env.NUXT_FIREBASE_STORAGE_BUCKET || '',
            NUXT_FIREBASE_MESSAGING_SENDER_ID: process.env.NUXT_FIREBASE_MESSAGING_SENDER_ID || '',
            NUXT_FIREBASE_APP_ID: process.env.NUXT_FIREBASE_APP_ID || '',
            NUXT_PAYMENT_MAINTENANCE: process.env.NUXT_PAYMENT_MAINTENANCE == 'true' || false,
            NUXT_PAYPAL_ID: process.env.NUXT_PAYPAL_ID || '',
            NUXT_RECAPCHA_V3_SITE_KEY:
                process.env.NUXT_RECAPCHA_V3_SITE_KEY || '6LeU33EnAAAAAGiKxemEFI878I2mcKtzHswbnxn8',
            NUXT_MODEL_GPT_4: process.env.NUXT_MODEL_GPT_4 == 'true' || false,
            NUXT_APP_VERSION: process.env.NUXT_APP_VERSION || '0.0.1',
            NUXT_MAINTENANCE_TRANSLATE_DOCUMENT: process.env.NUXT_MAINTENANCE_TRANSLATE_DOCUMENT || false,
            NUXT_SHOW_NOTIFICATION: process.env.NUXT_SHOW_NOTIFICATION == 'true' || false,
            NUXT_DISABLE_LOGIN_BY_SOCIAL: process.env.NUXT_DISABLE_LOGIN_BY_SOCIAL == 'true' || false,
            features: {
                ads: process.env.NUXT_FEATURE_ADS == 'true' || false,
            }
        },
    },
    css: ['vue-final-modal/style.css', '~/assets/css/style.css'],
    pwa: {
        workbox: {
            enabled: true, // Enable the service worker
            importScripts: ['custom-sw.js'],
        },
    },
    // googleAnalytics: {
    //     id: process.env.NUXT_GOOGLE_ANALYTICS_ID
    // },
    // publicRuntimeConfig: {
    //     googleAnalytics: {
    //         id: process.env.NUXT_GOOGLE_ANALYTICS_ID
    //     }
    // }
})
