<template>
    <div>
        <div class="relative">
            <select
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-500 placeholder-gray-300 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
            >
                <option selected>{{ props.placeholder }}</option>
                <option v-for="item in props.options" :value="item.value">{{ item.text }}</option>
                <slot/>
            </select>
        </div>
    </div>
</template>

<script setup lang="ts">
const props = defineProps<{
    label: string
    options: object
    placeholder: string
}>()
</script>
