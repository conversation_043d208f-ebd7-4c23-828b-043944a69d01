<template>
  <!-- Main modal -->
  <VueFinalModal
    class="fixed top-0 left-0 right-0 z-50 w-full p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[100vh] max-h-full"
  >
    <!-- Large Modal -->
    <div class="relative w-full max-w-lg max-h-full mx-auto">
      <!-- Modal content -->
      <div
        class="relative rounded-lg shadow bg-white text-black dark:bg-gray-700 dark:text-gray-50"
      >
        <!-- Modal header -->
        <div
          class="flex items-center justify-between p-5 border-b rounded-t dark:border-gray-900"
        >
          <h3 class="flex items-center text-xl font-medium">
            {{ $t("Delete account") }}
          </h3>
          <button
            @click="$emit('close')"
            type="button"
            class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ml-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
            data-modal-hide="large-modal"
          >
            <svg
              class="w-3 h-3"
              aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 14 14"
            >
              <path
                stroke="currentColor"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"
              />
            </svg>
            <span class="sr-only">Close modal</span>
          </button>
        </div>
        <!-- Modal body -->
        <div class="p-6 space-y-6 overflow-auto scrollbar-thin" style="max-height: 80vh">
          <div
            class="flex items-start p-4 mb-4 text-sm text-yellow-500 rounded-lg bg-yellow-50 dark:bg-gray-800 dark:text-yellow-300"
            role="alert"
          >
            <svg
              class="flex-shrink-0 inline w-4 h-4 me-3 mt-1 mr-2"
              aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"
              />
            </svg>
            <span class="sr-only">Info</span>
            <div>
              <p class="font-semibold">
                {{ $t("This account will be deleted permanently.") }}
              </p>
              <p>
                {{
                  $t(
                    "You are about to delete your account. This action cannot be undone."
                  )
                }}
              </p>
            </div>
          </div>

          <div class="mt-8">
            <label
              for="small-input"
              class="block mb-2 text-sm text-gray-900 dark:text-white"
            >
              <i18n-t keypath="Please type {0} to confirm." tag="p" class="mb-1 text-sm">
                <b> delete </b>
              </i18n-t>
            </label>
            <input
              type="text"
              v-model="confirmDeleteAccount"
              class="block w-full p-2 text-gray-900 border border-gray-300 rounded-lg bg-white text-xs focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-900 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
            />
          </div>
          <button
            type="button"
            :disabled="confirmDeleteAccount !== 'delete'"
            @click="userStore.deleteAccount"
            class="mt-3 justify-center w-full focus:ring-4 focus:outline-none focus:ring-gray-100 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center me-2 mb-2"
            :class="{
              'text-gray-50 bg-red-500 hover:bg-red-300 border border-red-300 ':
                confirmDeleteAccount === 'delete',
              'cursor-not-allowed text-red-500 bg-gray-100 hover:bg-gray-200 border border-gray-200 dark:focus:ring-gray-600 dark:bg-gray-600 dark:border-gray-700 dark:text-white dark:hover:bg-gray-700':
                confirmDeleteAccount !== 'delete',
            }"
          >
            <div v-if="isDeletingAccount" role="status" class="w-full text-center my-1">
              <svg
                aria-hidden="true"
                class="inline w-5 h-5 mr-2 text-gray-200 animate-spin dark:text-gray-200 fill-primary-600"
                viewBox="0 0 100 101"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                  fill="currentColor"
                />
                <path
                  d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                  fill="currentFill"
                />
              </svg>
              <span class="sr-only">Loading...</span>
            </div>
            <div v-else>
              {{ $t("I understand the consequences, delete this account") }}
            </div>
          </button>
        </div>
      </div>
    </div>
  </VueFinalModal>
</template>

<script lang="ts" setup>
import { VueFinalModal } from "vue-final-modal";
import { storeToRefs } from "pinia";
import { useUserStore } from "~/stores/user";
const userStore = useUserStore();

const { confirmDeleteAccount, isDeletingAccount } = storeToRefs(userStore);
const config = useRuntimeConfig();

const emit = defineEmits(["submit", "close"]);
</script>
