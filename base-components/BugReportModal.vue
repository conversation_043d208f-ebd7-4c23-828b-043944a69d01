<template>
    <!-- Main modal -->
    <VueFinalModal
        class="fixed top-0 left-0 right-0 z-50 w-full p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%)] max-h-full"
    >
        <!-- Large Modal -->
        <div class="relative w-full max-w-2xl max-h-full mx-auto">
            <!-- Modal content -->
            <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                <!-- Modal header -->
                <div class="flex items-center justify-between p-5 border-b rounded-t dark:border-gray-600">
                    <h3 class="flex items-center text-xl font-medium text-gray-900 dark:text-white">
                        <span
                            class="flex items-center justify-center w-8 h-8 bg-red-100 rounded-full dark:bg-red-600 mr-3"
                        >
                            <svg
                                class="w-5 h-5 text-white-800 dark:text-white-300"
                                aria-hidden="true"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 19 20"
                            >
                                <path
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="2"
                                    d="M7 3 6 2V1m5 2 1-1V1M9 7v11M9 7a5 5 0 0 1 5 5M9 7a5 5 0 0 0-5 5m5-5a4.959 4.959 0 0 1 2.973 1H12V6a3 3 0 0 0-6 0v2h.027A4.959 4.959 0 0 1 9 7Zm-5 5H1m3 0v2a5 5 0 0 0 10 0v-2m3 0h-3m-9.975 4H2a1 1 0 0 0-1 1v2m13-3h2.025a1 1 0 0 1 1 1v2M13 9h2.025a1 1 0 0 0 1-1V6m-11 3H3a1 1 0 0 1-1-1V6"
                                />
                            </svg>
                        </span>
                        {{ $t('Report a bug') }}
                    </h3>
                    <button
                        @click="$emit('close')"
                        type="button"
                        class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ml-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                        data-modal-hide="large-modal"
                    >
                        <svg
                            class="w-3 h-3"
                            aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 14 14"
                        >
                            <path
                                stroke="currentColor"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"
                            />
                        </svg>
                        <span class="sr-only">Close modal</span>
                    </button>
                </div>
                <!-- Modal body -->
                <div class="flex flex-col p-6 space-y-6">
                    <div class="relative mb-6">
                        <label for="helper-text" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                            >Transaction ID</label
                        >
                        <input
                            type="text"
                            ref="IDInput"
                            class="block w-full p-4 bg-gray-200 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-800 dark:border-gray-600 dark:placeholder-gray-500 placeholder-gray-300 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                            :value="uuid"
                            maxlength="50"
                            readonly
                        />
                        <button
                            @click="copyResult"
                            class="flex flex-inline text-white absolute right-2.5 bottom-2.5 font-medium rounded-lg text-sm px-2 py-2"
                            :class="'bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800'"
                        >
                            <svg
                                v-if="isCopied"
                                class="w-5 h-5 text-white dark:text-white animate-pulse"
                                aria-hidden="true"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                            >
                                <path
                                    d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z"
                                />
                            </svg>
                            <span v-else class="material-symbols-outlined"> content_copy </span>
                        </button>
                    </div>
                    <textarea
                        v-model="feedback"
                        rows="4"
                        class="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                        :placeholder="$t('Bug description')"
                    ></textarea>
                </div>
                <!-- Modal footer -->
                <div class="flex w-full items-center flex-inline justify-end px-6 pb-6 space-x-2 rounded-b">
                    <button
                        @click="onSubmit"
                        type="button"
                        class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-900 focus:z-10 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-500 dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-gray-600"
                    >
                        {{ $t('Submit report') }}
                    </button>
                </div>
            </div>
        </div>
    </VueFinalModal>
</template>

<script lang="ts" setup>
import { VueFinalModal } from 'vue-final-modal'
import { ref, onMounted, watch } from 'vue'
import { Modal } from 'flowbite'
import { useHistoryStore } from '~/stores/history'
const historyStore = useHistoryStore()
const emit = defineEmits(['submit', 'close'])
let modal = null
const props = defineProps({
    title: {
        type: String,
    },
    type: {
        type: String,
        default: 'like',
    },
    uuid: {
        type: String,
    },
})
const isCopied = ref(false)
const IDInput = ref<HTMLInputElement | null>(null)
const copyResult = () => {
    IDInput.value?.focus()
    IDInput.value?.select()
    document.execCommand('copy')
    isCopied.value = true
    setTimeout(() => {
        isCopied.value = false
    }, 1700)
}

const feedback = ref('')

const onSubmit = async () => {
    await historyStore.rateHistory(props.uuid, {
        rating: 'thumbs_down',
        rating_content: feedback.value,
    })
    emit('submit', feedback.value)
    emit('submit', feedback.value)
}
</script>
