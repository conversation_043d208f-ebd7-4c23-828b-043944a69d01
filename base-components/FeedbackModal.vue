<template>
    <!-- Main modal -->
    <VueFinalModal
        class="fixed top-0 left-0 right-0 z-50 w-full p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full"
    >
        <!-- Large Modal -->
        <div class="relative w-full max-w-4xl max-h-full mx-auto">
            <!-- Modal content -->
            <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                <!-- Modal header -->
                <div class="flex items-center justify-between p-5 border-b rounded-t dark:border-gray-600">
                    <h3 class="flex items-center text-xl font-medium text-gray-900 dark:text-white">
                        <span
                            v-if="type === 'like'"
                            class="flex items-center justify-center w-8 h-8 bg-green-100 rounded-full dark:bg-green-600 mr-3"
                        >
                            <svg
                                class="w-5 h-5 text-primary-800 dark:text-primary-300"
                                aria-hidden="true"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 20 20"
                            >
                                <path
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                    d="M4.008 8.714c1-.097 1.96-.45 2.792-1.028a25.112 25.112 0 0 0 4.454-5.72 1.8 1.8 0 0 1 .654-.706 1.742 1.742 0 0 1 1.65-.098 1.82 1.82 0 0 1 .97 1.128c.075.248.097.51.065.767l-1.562 4.629M4.008 8.714H1v9.257c0 .273.106.535.294.728a.99.99 0 0 0 .709.301h1.002a.99.99 0 0 0 .71-.301c.187-.193.293-.455.293-.728V8.714Zm8.02-1.028h4.968c.322 0 .64.08.925.232.286.153.531.374.716.645a2.108 2.108 0 0 1 .242 1.883l-2.36 7.2c-.288.813-.48 1.354-1.884 1.354-2.59 0-5.39-1.06-7.504-1.66"
                                />
                            </svg>
                        </span>
                        <span
                            v-else
                            class="flex items-center justify-center w-8 h-8 bg-red-100 rounded-full dark:bg-red-600 mr-3"
                        >
                            <svg
                                class="w-5 h-5 text-white-800 dark:text-white-300"
                                aria-hidden="true"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 20 20"
                            >
                                <path
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                    d="M15.992 11.287c-1 .097-1.96.45-2.792 1.029a25.118 25.118 0 0 0-4.454 5.721 1.803 1.803 0 0 1-.655.705 1.742 1.742 0 0 1-1.648.096 1.786 1.786 0 0 1-.604-.457 1.874 1.874 0 0 1-.432-1.439l1.562-4.626m9.023-1.03H19V2.03c0-.273-.106-.535-.294-.728A.99.99 0 0 0 17.997 1h-1.002a.99.99 0 0 0-.71.301 1.042 1.042 0 0 0-.293.728v9.258Zm-8.02 1.03H3.003c-.322 0-.64-.08-.925-.233a2.022 2.022 0 0 1-.716-.645 2.108 2.108 0 0 1-.242-1.883l2.36-7.2C3.769 1.54 3.96 1 5.365 1c2.59 0 5.39 1.06 7.504 1.66"
                                />
                            </svg>
                        </span>
                        {{ $t('Provide additional feedback') }}
                    </h3>
                    <button
                        @click="$emit('close')"
                        type="button"
                        class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ml-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                        data-modal-hide="large-modal"
                    >
                        <svg
                            class="w-3 h-3"
                            aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 14 14"
                        >
                            <path
                                stroke="currentColor"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"
                            />
                        </svg>
                        <span class="sr-only">Close modal</span>
                    </button>
                </div>
                <!-- Modal body -->
                <div class="p-6 space-y-6">
                    <textarea
                        v-model="feedback"
                        rows="4"
                        class="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                        :placeholder="
                            type === 'like'
                                ? $t('What do you like about the response?')
                                : $t('What was the issue with the response? How could it be improved?')
                        "
                    ></textarea>
                    <!-- <div v-if="type === 'dislike'" class="mt-4">
            <div v-for="reason in dislikeReasons" class="flex items-center mb-4">
              <input
                :id="reason"
                type="checkbox"
                :value="reason"
                class="w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 rounded focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
              />
              <label
                :for="reason"
                class="ml-2 text-sm font-medium text-gray-900 dark:text-gray-300"
                >{{ reason }}</label
              >
            </div>
          </div> -->
                </div>
                <!-- Modal footer -->
                <div class="flex w-full items-center flex-inline justify-end px-6 pb-6 space-x-2 rounded-b">
                    <button
                        @click="onSubmit"
                        type="button"
                        class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-900 focus:z-10 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-500 dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-gray-600"
                    >
                        {{ $t('Submit feedback') }}
                    </button>
                </div>
            </div>
        </div>
    </VueFinalModal>
</template>

<script lang="ts" setup>
import { VueFinalModal } from 'vue-final-modal'
import { ref, onMounted, watch } from 'vue'
import { Modal } from 'flowbite'
import { useHistoryStore } from '~/stores/history'
const historyStore = useHistoryStore()
const emit = defineEmits(['submit', 'close'])
let modal = null
const props = defineProps({
    title: {
        type: String,
    },
    type: {
        type: String,
        default: 'like',
    },
    uuid: {
        type: String,
    },
})

const dislikeReasons = ['This is harmful / unsafe', "This isn't true", "This isn't helpful"]

const feedback = ref('')

const onSubmit = async () => {
    await historyStore.rateHistory(props.uuid, {
        rating: props.type == 'like' ? 'thumbs_up' : 'thumbs_down',
        rating_content: feedback.value,
    })
    emit('submit', feedback.value)
}
</script>
