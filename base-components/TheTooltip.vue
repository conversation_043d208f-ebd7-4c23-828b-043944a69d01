<template>
    <div
        :id="'tooltipButton' + tooltipIdRandom"
        aria-current="page"
    >
       <slot></slot>
    </div>
    <div
        :id="'tooltip' + tooltipIdRandom"
        role="tooltip"
        class="absolute z-30 max-w-[277px] text-left invisible inline-block px-3 py-2 text-sm font-medium text-white bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip dark:bg-gray-700"
    >
        <slot name="tooltip"></slot>
        <div class="tooltip-arrow" data-popper-arrow></div>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { Tooltip } from 'flowbite';
const tooltipIdRandom = ref(Math.random().toString(36).substring(7))

onMounted(() => {
    if (process.client) {
        const $targetEl: HTMLElement = document.getElementById('tooltip' + tooltipIdRandom.value)
        const $triggerEl: HTMLElement = document.getElementById('tooltipButton' + tooltipIdRandom.value);
        const tooltip = new Tooltip($targetEl, $triggerEl, {
            triggerType: 'hover',
        })
    }
})
</script>
