#!/bin/bash
set -euf -o pipefail

# This script is used to invalidate the cache of a CloudFlare Cache.

# Usage:
#   invalidation-cache.sh <zone_id> <api_key>

# Arguments:
#   zone_id: The zone id of the CloudFlare account.
#   api_key: The api key of the CloudFlare account.


zone_id=$1
api_key=$2

echo "[CloudFlare] Invalidating cache..."
curl https://api.cloudflare.com/client/v4/zones/${zone_id}/purge_cache \
    -H 'Content-Type: application/json' \
    -H "Authorization: Bearer ${api_key}" \
    -d '{"purge_everything":true}'
echo "[CloudFlare] Cache invalidated with purge_everything=true"