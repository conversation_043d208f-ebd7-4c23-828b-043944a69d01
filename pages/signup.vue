<script setup>
import { reactive, ref, onUnmounted } from 'vue'
import Loading from 'vue-loading-overlay'
import TheLogoTranslate from '~/components/TheLogoTranslate.vue'
import { useAuthStore } from '~/stores/auth'
import { storeToRefs } from 'pinia'
import { useAppStore } from '~/stores/app'
import { useVuelidate } from '@vuelidate/core'
import * as validators from '@vuelidate/validators'
import { createI18nMessage } from '@vuelidate/validators'
import { useI18n } from 'vue-i18n'
const config = useRuntimeConfig()
const router = useRouter()
const route = useRoute()
const i18n = useI18n()
const authStore = useAuthStore()
const appStore = useAppStore()
definePageMeta({
    layout: false,
})
const withI18nMessage = createI18nMessage({ t: i18n.t.bind(i18n) })
const { isSigningUp, isError, registerError } = storeToRefs(authStore)
const { acceptedTermsOfService } = storeToRefs(appStore)
const required = withI18nMessage(validators.required)
const email = withI18nMessage(validators.email)
const minLength = (v) => withI18nMessage(validators.minLength(v))
const sameAs = (v) => withI18nMessage(validators.sameAs(v))
const formSignUpState = reactive({
    full_name: '',
    email: '',
    password: '',
    confirm_password: '',
    invitation_code: '',
    origin: '',
})

const formSignUpRules = computed(() => ({
    full_name: { required },
    email: { required, email },
    password: { required, minLength: minLength(6) },
    confirm_password: { required, minLength: minLength(6), sameAsPassword: sameAs(formSignUpState.password) },
    invitation_code: { minLength: minLength(12) },
}))
const v$ = useVuelidate(formSignUpRules, formSignUpState)
const signedUp = ref(false)
const countDown = ref(5)

const onSignUp = async () => {
    const result = await v$.value.$validate()
    if (!result) {
        console.log('Validation failed')
        console.log(v$.value.$errors[0])
        return
    }

    const success = await authStore.signup({
        full_name: formSignUpState.full_name,
        email: formSignUpState.email,
        password: formSignUpState.password,
        invitation_code: formSignUpState.invitation_code,
        origin: formSignUpState.origin,
    })
    if (success) {
        signedUp.value = true
        setInterval(() => {
            countDown.value--
        }, 1000)

        setTimeout(() => {
            router.push('/')
        }, 5000)
    }
}

onUnmounted(() => {
    acceptedTermsOfService.value = false
})

const invitationCodeEditable = ref(true)
onMounted(() => {
    const { invitation_code, origin } = route.query
    if (invitation_code) {
        formSignUpState.invitation_code = invitation_code
        invitationCodeEditable.value = false
    }

    if (origin) {
        formSignUpState.origin = origin
    }
})

const canLoginBySocial = !config.public.NUXT_DISABLE_LOGIN_BY_SOCIAL
</script>

<template>
    <NuxtLayout name="animation">
        <div class="flex flex-col items-center px-6 py-8 mx-auto md:min-h-screen lg:pt-4">
            <div class="flex items-center text-white mb-6 w-full sm:max-w-md">
                <TheLogoTranslate />
            </div>
            <div
                class="relative w-full bg-white rounded-lg shadow dark:border md:mt-0 sm:max-w-md xl:p-0 dark:bg-gray-800 dark:border-gray-700 z-20"
            >
                <loading height="35" v-model:active="isSigningUp" :is-full-page="false" />
                <div class="p-6 space-y-2 md:space-y-4 sm:p-8">
                    <h1
                        v-if="!signedUp"
                        class="text-xl font-bold leading-tight tracking-tight text-gray-900 md:text-2xl dark:text-white"
                    >
                        {{ $t('Create an account') }}
                    </h1>
                    <div v-if="canLoginBySocial && !signedUp">
                        <div class="grid grid-cols-1 md:grid-cols-1 gap-3">
                            <LoginByGoogle />
                            <!-- <LoginByApple /> -->
                        </div>
                        <div class="inline-flex items-center justify-center w-full">
                            <hr class="w-full h-px my-0 bg-gray-200 border-0 dark:bg-gray-700" />
                            <span
                                class="absolute px-3 text-sm font-light text-gray-900 -translate-x-1/2 bg-white left-1/2 dark:text-white dark:bg-gray-800"
                            >
                                {{ $t('OR') }}
                            </span>
                        </div>
                    </div>
                    <form v-if="!signedUp" @submit.prevent="onSignUp" class="space-y-2 md:space-y-3">
                        <div>
                            <label for="full_name" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
                                {{ $t('Your name') }}
                            </label>
                            <input
                                type="text"
                                name="full_name"
                                id="full_name"
                                v-model="formSignUpState.full_name"
                                class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-500 placeholder-gray-300 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                :class="{
                                    'bg-red-50 border border-red-500 text-red-900 placeholder-red-700 focus:ring-red-500 dark:bg-gray-700 focus:border-red-500 dark:text-red-500 dark:placeholder-red-500 dark:border-red-500':
                                        v$.full_name.$error,
                                }"
                                placeholder="Jude Hunter"
                                required=""
                            />
                            <p v-if="v$.full_name.$errors" class="mt-2 text-sm text-red-600 dark:text-red-500">
                                {{ v$.full_name.$errors[0]?.$message }}
                            </p>
                        </div>
                        <div>
                            <label for="email" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
                                {{ $t('Your email') }}
                            </label>
                            <input
                                type="email"
                                name="email"
                                id="email"
                                v-model="formSignUpState.email"
                                class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-500 placeholder-gray-300 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                :class="{
                                    'bg-red-50 border border-red-500 text-red-900 placeholder-red-700 focus:ring-red-500 dark:bg-gray-700 focus:border-red-500 dark:text-red-500 dark:placeholder-red-500 dark:border-red-500':
                                        v$.email.$error || registerError,
                                }"
                                placeholder="<EMAIL>"
                                required=""
                            />
                            <p
                                v-if="v$.email.$errors || registerError"
                                class="mt-2 text-sm text-red-600 dark:text-red-500"
                            >
                                {{ v$.email.$errors[0]?.$message || i18n.t(registerError?.detail.error_message || '') }}
                            </p>
                        </div>
                        <div>
                            <label for="password" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
                                {{ $t('Password') }}
                            </label>
                            <input
                                type="password"
                                name="password"
                                id="password"
                                v-model="formSignUpState.password"
                                placeholder="••••••••"
                                class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-500 placeholder-gray-300 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                :class="{
                                    'bg-red-50 border border-red-500 text-red-900 placeholder-red-700 focus:ring-red-500 dark:bg-gray-700 focus:border-red-500 dark:text-red-500 dark:placeholder-red-500 dark:border-red-500':
                                        v$.password.$error,
                                }"
                                required=""
                            />
                            <p v-if="v$.password.$errors" class="mt-2 text-sm text-red-600 dark:text-red-500">
                                {{ v$.password.$errors[0]?.$message }}
                            </p>
                        </div>
                        <div>
                            <label
                                for="confirm_password"
                                class="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                            >
                                {{ $t('Confirm password') }}
                            </label>
                            <input
                                type="password"
                                name="confirm_password"
                                id="confirm_password"
                                v-model="formSignUpState.confirm_password"
                                placeholder="••••••••"
                                class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-500 placeholder-gray-300 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                :class="{
                                    'bg-red-50 border border-red-500 text-red-900 placeholder-red-700 focus:ring-red-500 dark:bg-gray-700 focus:border-red-500 dark:text-red-500 dark:placeholder-red-500 dark:border-red-500':
                                        v$.confirm_password.$error,
                                }"
                                required=""
                            />
                            <p v-if="v$.confirm_password.$errors" class="mt-2 text-sm text-red-600 dark:text-red-500">
                                {{ v$.confirm_password.$errors[0]?.$message }}
                            </p>
                        </div>
                        <div>
                            <label
                                for="invitation_code"
                                class="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                            >
                                {{ $t('Invitation code (Optional)') }}
                            </label>
                            <input
                                type="text"
                                name="invitation_code"
                                id="invitation_code"
                                v-model="formSignUpState.invitation_code"
                                placeholder="abcdef1234567890"
                                class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-500 placeholder-gray-300 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                :class="{
                                    'bg-red-50 border border-red-500 text-red-900 placeholder-red-700 focus:ring-red-500 dark:bg-gray-700 focus:border-red-500 dark:text-red-500 dark:placeholder-red-500 dark:border-red-500':
                                        v$.invitation_code.$error,
                                }"
                                :disabled="!invitationCodeEditable"
                            />
                            <p v-if="v$.invitation_code.$errors" class="mt-2 text-sm text-red-600 dark:text-red-500">
                                {{ v$.invitation_code.$errors[0]?.$message }}
                            </p>
                        </div>
                        <div class="flex items-start">
                            <div class="flex items-center h-5">
                                <input
                                    id="terms"
                                    aria-describedby="terms"
                                    type="checkbox"
                                    class="w-4 h-4 text-primary-600 border border-gray-300 rounded bg-gray-50 focus:ring-3 focus:ring-primary-300 dark:bg-gray-700 dark:border-gray-600 dark:focus:ring-primary-600 dark:ring-offset-gray-800"
                                    required=""
                                    v-model="acceptedTermsOfService"
                                />
                            </div>
                            <div class="ml-3 text-sm">
                                <label for="terms" class="font-light text-gray-500 dark:text-gray-300">
                                    <i18n-t keypath="accept-terms" tag="span">
                                        <a
                                            @click="appStore.setShowTermsOfServiceModal(true)"
                                            class="font-medium text-primary-600 hover:underline dark:text-primary-500 cursor-pointer"
                                        >
                                            {{ $t('Terms of Service') }}
                                        </a>
                                    </i18n-t>
                                </label>
                            </div>
                        </div>
                        <button
                            :disabled="isSigningUp"
                            type="submit"
                            class="w-full text-white bg-primary-600 hover:bg-primary-700 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
                        >
                            {{ $t('Create an account') }}
                        </button>
                        <p class="text-sm font-light text-gray-500 dark:text-gray-400">
                            {{ $t('Already have an account?') }}
                            <a
                                @click="router.push({ path: '/signin' })"
                                class="font-medium text-primary-600 hover:underline dark:text-primary-500 cursor-pointer"
                            >
                                {{ $t('Sign In') }}
                            </a>
                        </p>
                    </form>
                    <div
                        v-if="signedUp"
                        id="alert-additional-content-3"
                        class="p-4 mb-4 text-green-800 border border-green-300 rounded-lg bg-green-50 dark:bg-gray-800 dark:text-green-400 dark:border-green-800"
                        role="alert"
                    >
                        <div class="flex items-center">
                            <svg
                                class="flex-shrink-0 w-4 h-4 mr-2"
                                aria-hidden="true"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                            >
                                <path
                                    d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"
                                />
                            </svg>
                            <span class="sr-only">Info</span>
                            <h3 class="text-lg font-medium">{{ $t('Signup successfully') }}</h3>
                        </div>
                        <div class="mt-2 mb-4 text-sm">
                            {{ $t('Your account has been created, ') }}
                            <span class="text-yellow-800 dark:text-yellow-300">{{
                                $t('please check your mailbox to activate your account.')
                            }}</span>
                        </div>
                        <div class="mt-2 mb-4 text-sm">
                            {{
                                $t('After few seconds, you will be redirected to the home page.', { value: +countDown })
                            }}
                        </div>
                        <div class="flex">
                            <button
                                @click="router.push('/')"
                                type="button"
                                class="text-white bg-green-800 hover:bg-green-900 focus:ring-4 focus:outline-none focus:ring-green-300 font-medium rounded-lg text-xs px-3 py-1.5 mr-2 text-center inline-flex items-center dark:bg-green-600 dark:hover:bg-green-700 dark:focus:ring-green-800"
                            >
                                <svg
                                    class="-ml-0.5 mr-2 h-3 w-3"
                                    aria-hidden="true"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="currentColor"
                                    viewBox="0 0 20 14"
                                >
                                    <path
                                        d="M10 0C4.612 0 0 5.336 0 7c0 1.742 3.546 7 10 7 6.454 0 10-5.258 10-7 0-1.664-4.612-7-10-7Zm0 10a3 3 0 1 1 0-6 3 3 0 0 1 0 6Z"
                                    />
                                </svg>
                                {{ $t('Redirect now') }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </NuxtLayout>
</template>
