<script setup>
import { reactive } from 'vue'
import Loading from 'vue-loading-overlay'
import TheLogoTranslate from '~/components/TheLogoTranslate.vue'
import { useAuthStore } from '~/stores/auth'
import { storeToRefs } from 'pinia'
import { useAppStore } from '~/stores/app'
import { useVuelidate } from '@vuelidate/core'
import * as validators from '@vuelidate/validators'
import { createI18nMessage } from '@vuelidate/validators'
import { useI18n } from 'vue-i18n'
import { useRoute } from 'vue-router'
const router = useRouter()
const i18n = useI18n()
const route = useRoute()
const authStore = useAuthStore()
const appStore = useAppStore()
const { resetPasswordError, resetPasswordEmail } = storeToRefs(authStore)
definePageMeta({
    layout: false
})
const withI18nMessage = createI18nMessage({ t: i18n.t.bind(i18n) })
const isLoading = ref(false)
const required = withI18nMessage(validators.required)
const email = withI18nMessage(validators.email)
const minLength = (v) => withI18nMessage(validators.minLength(v))
const sameAs = (v) => withI18nMessage(validators.sameAs(v))
const formState = reactive({
    password: '',
    confirm_password: ''
})

const isSuccess = ref(false)
const token = computed(() => route.query.token)
const formRules = computed(() => ({
    password: { required, minLength: minLength(6) },
    confirm_password: { required, minLength: minLength(6), sameAsPassword: sameAs(formState.password) }
}))
const v$ = useVuelidate(formRules, formState)


const onResetPassword = async () => {
    const result = await v$.value.$validate()
    if (!result) {
        console.log('Validation failed')
        console.log(v$.value.$errors[0])
        return
    }

    isLoading.value = true
    const success = await authStore.resetPassword({ token: token, new_password: formState.password })
    isLoading.value = false
    if (success) {
        isSuccess.value = true
        const loginSuccess = await authStore.login({ username: resetPasswordEmail.value, password: formState.password })
        if (loginSuccess) {
            setTimeout(async () => {
                window.location.href = "/"
            }, 5000)
        }
    }
}

</script>

<template>
    <NuxtLayout name='animation'>
        <div class='flex flex-col items-center justify-center px-6 py-8 mx-auto md:h-screen lg:py-0'>
            <div class='flex items-center text-white mb-6 w-full sm:max-w-md'>
                <TheLogoTranslate />
            </div>
            <div
                class='w-full bg-white rounded-lg shadow dark:border md:mt-0 sm:max-w-md xl:p-0 dark:bg-gray-800 dark:border-gray-700 z-20'
            >
                <loading height='35' v-model:active='isLoading' :is-full-page='false' />
                <div class='p-6 space-y-4 md:space-y-6 sm:p-8'>
                    <h1
                        class='text-xl font-bold leading-tight tracking-tight text-gray-900 md:text-2xl dark:text-white'
                    >
                        {{ $t('Reset password') }}
                    </h1>
                    <form @submit.prevent='onResetPassword' class='space-y-4 md:space-y-6'>
                        <div>
                            <label for='password' class='block mb-2 text-sm font-medium text-gray-900 dark:text-white'>
                                {{ $t('New password') }}
                            </label>
                            <input
                                type='password'
                                name='password'
                                id='password'
                                v-model='formState.password'
                                placeholder='••••••••'
                                class='bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-500 placeholder-gray-300 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500'
                                :class="{
                                    'bg-red-50 border border-red-500 text-red-900 placeholder-red-700 focus:ring-red-500 dark:bg-gray-700 focus:border-red-500 dark:text-red-500 dark:placeholder-red-500 dark:border-red-500':
                                        v$.password.$error,
                                }"
                                required=''
                            />
                            <p v-if='v$.password.$errors' class='mt-2 text-sm text-red-600 dark:text-red-500'>
                                {{ v$.password.$errors[0]?.$message }}
                            </p>
                        </div>
                        <div>
                            <label
                                for='confirm_password'
                                class='block mb-2 text-sm font-medium text-gray-900 dark:text-white'
                            >
                                {{ $t('Confirm password') }}
                            </label>
                            <input
                                type='password'
                                name='confirm_password'
                                id='confirm_password'
                                v-model='formState.confirm_password'
                                placeholder='••••••••'
                                class='bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-500 placeholder-gray-300 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500'
                                :class="{
                                    'bg-red-50 border border-red-500 text-red-900 placeholder-red-700 focus:ring-red-500 dark:bg-gray-700 focus:border-red-500 dark:text-red-500 dark:placeholder-red-500 dark:border-red-500':
                                        v$.confirm_password.$error,
                                }"
                                required=''
                            />
                            <p v-if='v$.confirm_password.$errors' class='mt-2 text-sm text-red-600 dark:text-red-500'>
                                {{ v$.confirm_password.$errors[0]?.$message }}
                            </p>
                        </div>
                        <div v-if="isSuccess" id="alert-3" class="flex p-4 mb-4 text-sm text-green-800 border border-green-300 rounded-lg bg-green-50 dark:bg-gray-800 dark:text-green-400 dark:border-green-800" role="alert">
                            <svg aria-hidden="true" class="flex-shrink-0 w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path></svg>
                            <span class="sr-only">Info</span>
                            <div class="ml-3 text-sm font-medium">
                                {{ $t('Password changed successfully! Returning to homepage...') }}
                            </div>
                            <button @click="isSuccess = false" type="button" class="ml-auto -mx-1.5 -my-1.5 bg-green-50 text-green-500 rounded-lg focus:ring-2 focus:ring-green-400 p-1.5 hover:bg-green-200 inline-flex h-8 w-8 dark:bg-gray-800 dark:text-green-400 dark:hover:bg-gray-700" data-dismiss-target="#alert-3" aria-label="Close">
                                <span class="sr-only">Close</span>
                                <svg aria-hidden="true" class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
                            </button>
                        </div>
                        <div v-if="resetPasswordError" id="alert-2" class="flex p-4 mb-4 text-sm text-red-800 border border-red-300 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400 dark:border-red-800" role="alert">
                            <svg aria-hidden="true" class="flex-shrink-0 w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path></svg>
                            <span class="sr-only">Info</span>
                            <div class="ml-3 text-sm font-medium">
                                {{ i18n.t(resetPasswordError?.detail.error_message || "" )}}
                            </div>
                            <button type="button" class="ml-auto -mx-1.5 -my-1.5 bg-red-50 text-red-500 rounded-lg focus:ring-2 focus:ring-red-400 p-1.5 hover:bg-red-200 inline-flex h-8 w-8 dark:bg-gray-800 dark:text-red-400 dark:hover:bg-gray-700" data-dismiss-target="#alert-2" aria-label="Close">
                                <span class="sr-only">Close</span>
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
                            </button>
                        </div>
                        <button
                            :disabled='isLoading'
                            type='submit'
                            class='w-full text-white bg-primary-600 hover:bg-primary-700 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800'
                        >
                            {{ $t('Submit') }}
                        </button>
                        <p class='text-sm font-light text-gray-500 dark:text-gray-400'>
                            {{ $t('Already have an account?') }}
                            <a
                                @click="router.push({ path: '/signin' })"
                                class='font-medium text-primary-600 hover:underline dark:text-primary-500 cursor-pointer'
                            >
                                {{ $t('Sign in') }}
                            </a
                            >
                        </p>
                    </form>
                </div>
            </div>
        </div>
    </NuxtLayout>
</template>