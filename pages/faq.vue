<script setup lang="ts">
definePageMeta({
    layout: false,
})

const faqs = [
    {
        q: 'How does the document translation feature work?',
        a: 'To translate a document, you need to upload the file to our website. Once uploaded, our system securely processes the document and provides you with the translated version. Your uploaded files are promptly deleted from our servers after the translation is completed.',
    },
    {
        q: 'How is the cost of translation calculated?',
        a: 'Our translation cost is calculated based on the number of tokens used, with each token determined by the OpenAI tokenizer algorithm. This algorithm ensures accurate counting of words in the input text or document, allowing precise billing for the translation service.',
    },
    {
        q: 'Is my payment information secure?',
        a: 'Yes, we prioritize the security of your payment information. We do not store any credit card information on our servers. All payment transactions are securely processed by trusted third-party payment service providers, adhering to their respective privacy and security policies.',
    },
    {
        q: 'Will I receive an email when the document translation is completed?',
        a: 'Yes, upon completion of the document translation process, you will receive an email notification containing the download URL for the translated document. This allows you to securely access and retrieve the translated file. The download link will remain active for a specified period of time for your convenience.',
    },
    {
        q: 'Can I change my email address?',
        a: 'No, you cannot change your email address once your account has been created. This policy ensures the security and integrity of your account information. However, you can update your full name and password within your account settings.',
    },
    {
        q: 'How do you protect my personal information and uploaded documents?',
        a: 'We take the security of your personal information and uploaded documents seriously. We employ industry-standard measures to protect them from unauthorized access, disclosure, alteration, and destruction. However, please be aware that no method of transmission over the internet or electronic storage is completely secure. Therefore, we cannot guarantee absolute security.',
    },
    {
        q: 'What are DocTransGPT tokens?',
        a: 'Tokens are pieces of words used for natural language processing (NLP). Depending on the language or difficulty of the text, the number of tokens used will vary. For text in English - GPT-3.5 model: 1,000 words ~ 3,000 tokens, GPT-4 model: 1,000 words ~ 90,000 tokens.',
    },
]
</script>

<template>
    <NuxtLayout name="non-translate">
        <div class="min-h-[calc(100vh-130px)] py-6 pb-24 space-y-4">
            <main class="pt-4 pb-16 lg:pt-4 lg:pb-24 bg-white dark:bg-gray-900">
                <section class="bg-white dark:bg-gray-900">
                    <div class="pb-8 px-4 mx-auto max-w-screen-xl sm:pb-16 lg:px-6">
                        <h2 class="mb-8 text-4xl tracking-tight font-extrabold text-gray-900 dark:text-white">
                            {{ $t('Frequently asked questions') }}
                        </h2>
                        <div
                            class="grid pt-8 text-left border-t border-gray-200 md:gap-16 dark:border-gray-700 md:grid-cols-2"
                        >
                            <div v-for="row in faqs" class="mb-0">
                                <h3 class="flex items-center mb-4 text-lg font-medium text-gray-900 dark:text-white">
                                    <svg
                                        class="flex-shrink-0 mr-2 w-5 h-5 text-gray-500 dark:text-gray-400"
                                        fill="currentColor"
                                        viewBox="0 0 20 20"
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <path
                                            fill-rule="evenodd"
                                            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z"
                                            clip-rule="evenodd"
                                        ></path>
                                    </svg>
                                    {{ $t(row.q) }}
                                </h3>
                                <p class="text-gray-500 dark:text-gray-400">
                                    {{ $t(row.a) }}
                                </p>
                            </div>
                        </div>
                    </div>
                </section>
            </main>
        </div>
    </NuxtLayout>
</template>
