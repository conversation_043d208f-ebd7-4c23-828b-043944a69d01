<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { storeToRefs } from 'pinia'
import TranslateButtonPC from '~/components/TranslateButtonPC.vue'
import TranslateResult from '~/components/TranslateResult.vue'
import TranslateInputText from '~/components/TranslateInputText.vue'
import TranslateOptions from '~/components/TranslateOptions.vue'
import { useTranslateStore } from '~~/stores/translate'
import { useAppStore } from '~~/stores/app'
import { useI18n } from 'vue-i18n'
import { useAuthStore } from '~~/stores/auth'
const authStore = useAuthStore()
const { showAds, user, isLoggedIn, isUnverified } = storeToRefs(authStore)
const translateStore = useTranslateStore()
const appStore = useAppStore()
const { t, locale } = useI18n({ useScope: 'global' })
const { tg } = storeToRefs(appStore)
const { inputText, showCustomPrompt, customPrompt, result } = storeToRefs(translateStore)
onMounted(() => {
    translateStore.initSelectedLanguages()

    nextTick(async () => {
        tg.value?.addSteps([
            {
                order: 3,
                group: 'translate-text',
                target: '#from-language-select',
                content: t('tour_guide.from_language'),
                title: t('From language'),
            },
            {
                order: 4,
                group: 'translate-text',
                target: '#btn-toggle-select-language-from',
                content: t('tour_guide.click_search_more_from_language'),
                title: t('Search more languages'),
            },
            {
                order: 5,
                group: 'translate-text',
                target: '#language-options-select',
                content: t('tour_guide.select_more_from_language'),
                title: t('Filter and select language'),
                beforeEnter: async () => {
                    return new Promise(async (resolve) => {
                        await translateStore.setSelectLanguageFor('from')
                        await tg.value?.updatePositions()
                        return resolve(true)
                    })
                },
                afterLeave: async () => {
                    return new Promise(async (resolve) => {
                        await translateStore.selectLanguage('from', 'english')
                        await translateStore.setOpenSelectLanguage(false)
                        await tg.value?.updatePositions()
                        return resolve(true)
                    })
                },
            },
            {
                order: 6,
                group: 'translate-text',
                target: '#to-language-select',
                content: t('tour_guide.to_language'),
                title: t('To language'),
                afterLeave: async () => {
                    return new Promise(async (resolve) => {
                        await translateStore.selectLanguage('to', 'vietnamese')
                        await tg.value?.updatePositions()
                        return resolve(true)
                    })
                },
            },
            {
                order: 7,
                group: 'translate-text',
                target: '#text-translate-input',
                content: t('tour_guide.text_input'),
                title: t('Text input'),
                beforeEnter: async () => {
                    return new Promise(async (resolve) => {
                        inputText.value = t(
                            'Hi, I am DocTransGPT. I can translate many languages for you with high accuracy. I hope you will like me'
                        )
                        await tg.value?.updatePositions()
                        return resolve(true)
                    })
                },
            },
            {
                order: 8,
                group: 'translate-text',
                target: '#translate-options',
                content: t('tour_guide.translate_options'),
                title: t('Translate options'),
            },
            {
                order: 9,
                group: 'translate-text',
                target: '#btn-toggle-custom-prompt',
                content: t('tour_guide.select_custom_prompt'),
                title: t('Custom prompt'),
                afterLeave: async () => {
                    return new Promise(async (resolve) => {
                        showCustomPrompt.value = true

                        //sleep 1s
                        // await new Promise((resolve) => setTimeout(resolve, 500))
                        await tg.value?.refresh()
                        await tg.value?.refreshDialog()
                        await tg.value?.updatePositions()

                        return resolve(true)
                    })
                },
            },
            {
                order: 10,
                group: 'translate-text',
                target: '#custom-prompt-input',
                content: t('tour_guide.input_custom_prompt'),
                title: t('Input your custom prompt'),
            },
            {
                order: 11,
                group: 'translate-text',
                target: '#suggest-custom-promts',
                content: t('tour_guide.select_suggested_custom_prompt'),
                title: t('Or select from suggested prompts'),
                afterLeave: async () => {
                    return new Promise(async (resolve) => {
                        customPrompt.value = t('Translate and write like an email')
                        return resolve(true)
                    })
                },
            },
            {
                order: 12,
                group: 'translate-text',
                target: '#translate-text-button',
                content: t('tour_guide.click_translate_button'),
                title: t('Translate Button'),
                afterLeave: async () => {
                    return new Promise(async (resolve) => {
                        result.value = `Chào bạn,\n\nTôi là DocTransGPT. Tôi có thể dịch nhiều ngôn ngữ cho bạn với độ chính xác cao. Tôi hy vọng bạn sẽ thích tôi.\n\nTrân trọng,\n\nDocTransGPT`
                        return resolve(true)
                    })
                },
            },
            {
                order: 13,
                group: 'translate-text',
                target: '#text-translate-result',
                content: t('tour_guide.translated_result'),
                title: t('Translated result'),
            },
            {
                order: 14,
                group: 'translate-text',
                target: '#btn-copy-result',
                content: t('tour_guide.copy_translated_result'),
                title: t('Copy translated result'),
            },
            {
                order: 15,
                group: 'translate-text',
                target: '#history-item-rate-text',
                content: t('tour_guide.rate_translated_result'),
                title: t('Rate translated result'),
            },
        ])

        let script = document.createElement('script')
        script.innerHTML = `(function(w,q){w[q]=w[q]||[];w[q].push(["_mgc.load"])})(
        window,"_mgq"
    );`
        document.body.appendChild(script)
    })
})
</script>

<template>
    <div class="h-full flex flex-col">
        <div class="relative flex-1 h-full grid grid-cols-1 md:grid-cols-2 gap-3 content-stretch">
            <div
                class="flex flex-col h-full border border-gray-200 rounded-lg bg-white dark:bg-gray-700 dark:border-gray-600"
            >
                <div class="flex-1">
                    <TranslateInputText />
                </div>
                <div id="translate-options" class="hidden shrink-0 md:block">
                    <TranslateOptions />
                </div>
            </div>
            <TranslateButtonPC id="translate-text-button" />
            <div class="relative flex-grow h-full">
                <TranslateResult />
            </div>
        </div>
        <div v-if="showAds" class="hidden md:block pt-2">
            <div data-type="_mgwidget" data-widget-id="1603197"></div>
        </div>
    </div>
</template>
