<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import { storeToRefs } from 'pinia'
import TranslateButtonPC from '~/components/TranslateButtonPC.vue'
import TranslateResultFiles from '~/components/TranslateResultFiles.vue'
import TranslateInputFiles from '~/components/TranslateInputFiles.vue'
import TranslateOptions from '~/components/TranslateOptions.vue'
import AccountVerifyWarning from '~/components/AccountVerifyWarning.vue'
import SignInSignUpRequire from '~/components/SignInSignUpRequire.vue'
import PopupModal from '~/base-components/PopupModal.vue'
import { useTranslateStore } from '~~/stores/translate'
import { useAuthStore } from '~~/stores/auth'
import { useAppStore } from '~~/stores/app'
import { useI18n } from 'vue-i18n'
import { useNotificationsStore } from '~~/stores/notifications'
const config = useRuntimeConfig()
const isMaintenance = config.public.NUXT_MAINTENANCE_TRANSLATE_DOCUMENT === 'true'
const notificationsStore = useNotificationsStore()
const { notifications } = storeToRefs(notificationsStore)
const authStore = useAuthStore()
const translateStore = useTranslateStore()
const {
    isHasLimited,
    resultFileLink,
    showWarningDocument,
    isUploading,
    inputFile,
    customPrompt,
    showCustomPrompt,
    documentResult,
    uploadProgress,
    isTranslating,
} = storeToRefs(translateStore)
const { showAds, user, isLoggedIn, isUnverified } = storeToRefs(authStore)
const appStore = useAppStore()
const { tg, showNotificationsDropdown } = storeToRefs(appStore)
const { t, locale } = useI18n({ useScope: 'global' })

onMounted(() => {
    translateStore.initSelectedLanguages()

    nextTick(async () => {
        let filePasswordInterval: any = null
        tg.value?.addSteps([
            {
                order: 3,
                group: 'translate-document',
                target: '#from-language-select',
                content: t('tour_guide.from_language'),
                title: t('From language'),
            },
            {
                order: 4,
                group: 'translate-document',
                target: '#btn-toggle-select-language-from',
                content: t('tour_guide.click_search_more_from_language'),
                title: t('Search more languages'),
            },
            {
                order: 5,
                group: 'translate-document',
                target: '#language-options-select',
                content: t('tour_guide.select_more_from_language'),
                title: t('Filter and select language'),
                beforeEnter: async () => {
                    return new Promise(async (resolve) => {
                        await translateStore.setSelectLanguageFor('from')
                        await tg.value?.updatePositions()
                        return resolve(true)
                    })
                },
                afterLeave: async () => {
                    return new Promise(async (resolve) => {
                        await translateStore.selectLanguage('from', 'english')
                        await translateStore.setOpenSelectLanguage(false)
                        await tg.value?.updatePositions()
                        return resolve(true)
                    })
                },
            },
            {
                order: 6,
                group: 'translate-document',
                target: '#to-language-select',
                content: t('tour_guide.to_language'),
                title: t('To language'),
                afterLeave: async () => {
                    return new Promise(async (resolve) => {
                        await translateStore.selectLanguage('to', 'vietnamese')
                        await tg.value?.updatePositions()
                        return resolve(true)
                    })
                },
            },
            {
                order: 7,
                group: 'translate-document',
                target: '#dropzone-file-input',
                content: t('tour_guide.upload_your_document'),
                title: t('Upload your document'),
                afterLeave: async (c, n) => {
                    if (c.order == n.order) return
                    return new Promise(async (resolve) => {
                        const file = new File([''], 'your_file.pdf', {
                            type: 'text/plain',
                        })
                        Object.defineProperty(file, 'size', { value: 1024 * 1024 * 1000 + 1 })
                        inputFile.value = file
                        await tg.value?.updatePositions()
                        //sleep 1s
                        await new Promise((resolve) => setTimeout(resolve, 500))
                        return resolve(true)
                    })
                },
            },
            {
                order: 8,
                group: 'translate-document',
                target: '#dropzone-file-input',
                content: t('tour_guide.password_protected_file'),
                title: t('Password protected file'),
            },
            {
                order: 9,
                group: 'translate-document',
                target: '#translate-options',
                content: t('tour_guide.translate_options'),
                title: t('Translate options'),
            },
            {
                order: 10,
                group: 'translate-document',
                target: '#btn-toggle-custom-prompt',
                content: t('tour_guide.select_custom_prompt'),
                title: t('Custom prompt'),
                beforeLeave: async (c, n) => {
                    if (c.order == n.order) return
                    return new Promise(async (resolve) => {
                        showCustomPrompt.value = true

                        //sleep 1s
                        // await new Promise((resolve) => setTimeout(resolve, 500))
                        await tg.value?.refresh()
                        await tg.value?.refreshDialog()
                        await tg.value?.updatePositions()

                        return resolve(true)
                    })
                },
            },
            {
                order: 11,
                group: 'translate-document',
                target: '#custom-prompt-input',
                content: t('tour_guide.input_custom_prompt'),
                title: t('Input your custom prompt'),
            },
            {
                order: 12,
                group: 'translate-document',
                target: '#suggest-custom-promts',
                content: t('tour_guide.select_suggested_custom_prompt'),
                title: t('Or select from suggested prompts'),
                afterLeave: async () => {
                    return new Promise(async (resolve) => {
                        customPrompt.value = t('Translate and write like a poem')
                        return resolve(true)
                    })
                },
            },
            {
                order: 13,
                group: 'translate-document',
                target: '#translate-document-button',
                content: t('tour_guide.click_translate_button'),
                title: t('Translate Button'),
                afterLeave: async (c, n) => {
                    if (c.order == n.order) return
                    return new Promise(async (resolve) => {
                        uploadProgress.value = 1
                        let interval = setInterval(() => {
                            if (uploadProgress.value >= 100) {
                                uploadProgress.value = 0
                                documentResult.value = 'STARTED'
                                clearInterval(interval)
                                return
                            }
                            uploadProgress.value += 1
                        }, 50)
                        return resolve(true)
                    })
                },
            },
            {
                order: 14,
                group: 'translate-document',
                target: '#dropzone-file-input',
                content: t('tour_guide.upload_and_translate_your_document'),
                title: t('Upload and translate your document'),
            },
            {
                order: 15,
                group: 'translate-document',
                content: t('tour_guide.translate_document_result'),
                title: t('Translate document result'),
            },
            {
                order: 16,
                group: 'translate-document',
                target: '#notification-icon',
                content: t('tour_guide.translate_document_result_notification'),
                title: t('Translate result notification'),
                beforeEnter: async () => {
                    return new Promise(async (resolve) => {
                        notificationsStore.addFakeNotification()
                        return resolve(true)
                    })
                },
                beforeLeave: async (c, n) => {
                    if (c.order == n.order) return
                    return new Promise(async (resolve) => {
                        showNotificationsDropdown.value = true
                        //sleep 1s
                        return resolve(true)
                    })
                },
            },
            {
                order: 17,
                group: 'translate-document',
                target: '#dropdownNotification',
                content: t('tour_guide.translate_document_result_notification_detail'),
                title: t('Translate result notification'),
            },
            {
                order: 18,
                group: 'translate-document',
                target: '#fake-notification-uuid .btn-download-result',
                content: t('tour_guide.download_translated_document'),
                title: t('Download translated document'),
                beforeEnter: async (c, n) => {
                    return new Promise(async (resolve) => {
                        showNotificationsDropdown.value = true
                        //sleep 1s
                        return resolve(true)
                    })
                },
            },
            {
                order: 19,
                group: 'translate-document',
                target: '#fake-notification-uuid .detail-translated-document-link',
                content: t('tour_guide.open_translated_document_history'),
                title: t('View detail translated document history'),
                beforeEnter: async (c, n) => {
                    return new Promise(async (resolve) => {
                        showNotificationsDropdown.value = true
                        //sleep 1s
                        return resolve(true)
                    })
                },
            },
        ])

        let script = document.createElement('script')
        script.innerHTML = `(function(w,q){w[q]=w[q]||[];w[q].push(["_mgc.load"])})(
        window,"_mgq"
    );`
        document.body.appendChild(script)
    })

    let script = document.createElement('script')
    script.innerHTML = `(function(w,q){w[q]=w[q]||[];w[q].push(["_mgc.load"])})(
        window,"_mgq"
    );`
    document.body.appendChild(script)
})

const isShowPopup = ref(false)
watch(resultFileLink, (value) => {
    if (value && isHasLimited.value) {
        isShowPopup.value = true
    }
})

const onCancel = () => {
    isShowPopup.value = false
    translateStore.clearInputFile()
}

onBeforeRouteLeave((to, from, next) => {
    if (isUploading.value) {
        if (confirm('Are you sure you want to leave this page? Your document is still uploading.') === true) {
            next()
            return
        } else {
            next(false)
            return
        }
    }

    next()
})

window.onbeforeunload = () => {
    if (isUploading.value) {
        if (confirm('Are you sure you want to leave this page? Your document is still uploading.') === true) {
            return true
        } else {
            return false
        }
    }
}

if (isMaintenance) {
}
</script>

<template>
    <div v-if="isMaintenance" class="text-center flex flex-col items-center justify-center">
        <img src="~/assets/images/maintenance.png" class="w-full sm:w-1/3" />
        <div class="text-3xl text-primary-500">
            {{ $t('This feature is currently down for maintenance') }}
        </div>
        <div class="text-xl font-thin">
            {{ $t('We apologize for any inconveniences caused.') }} <br />
            {{ $t("We've almost done.") }}
        </div>
    </div>
    <div v-else class="relative grid grid-cols-1 md:grid-cols-2 gap-3 content-stretch">
        <div
            class="flex flex-col h-full border border-gray-200 rounded-lg bg-white dark:bg-gray-700 dark:border-gray-600"
        >
            <div class="shrink-0">
                <TranslateInputFiles />
            </div>
            <div id="translate-options" class="hidden shrink-0 md:block">
                <TranslateOptions />
            </div>
        </div>
        <TranslateButtonPC id="translate-document-button" />
        <div class="relative flex-grow">
            <SignInSignUpRequire
                v-if="showWarningDocument"
                class="p-14"
                :title="$t('Want to translate the documents?')"
                :message="
                    $t('With the free version, you can translate text only. Sign up for free to translate documents.')
                "
            />
            <AccountVerifyWarning
                v-else-if="isLoggedIn && isUnverified"
                class="p-14 h-full flex justify-center flex-col pl-8"
            />
            <TranslateResultFiles v-else />
        </div>
        <PopupModal
            :isOpen="isShowPopup"
            :title="
                $t(
                    'We need time to translate this file. Once the translation is complete, the file will be sent to your email.'
                )
            "
            :btnText="$t('Ok, I will check my email')"
            @cancel="onCancel"
        />
    </div>
    <div class="pt-2">
        <!-- place in the body -->
        <div class="hidden md:block" v-if="showAds" data-type="_mgwidget" data-widget-id="1602670"></div>
    </div>
</template>
