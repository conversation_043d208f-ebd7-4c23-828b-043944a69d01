<script setup lang="ts">
definePageMeta({
    layout: false,
})
import { storeToRefs } from 'pinia'
import { useI18n } from 'vue-i18n'
import { ref, onMounted, watch } from 'vue'
import { useAppStore } from '~~/stores/app'
const { t, locale } = useI18n({ useScope: 'global' })
const appStore = useAppStore()
const { tg } = storeToRefs(appStore)
const guides = [
    {
        id: 'navigation',
        name: t('Navigation'),
        description: t(
            'You can navigate to any page on the website by clicking on the links in the navigation bar at the top of the page.'
        ),
    },
    {
        id: 'language',
        name: t('Languages'),
        description: t(
            'You can change the language of the website by clicking on the language icon in the navigation bar at the top of the page.'
        ),
    },
    {
        id: 'translate-text',
        name: t('Translate text'),
        description: t('You can translate text by clicking on the translate text menu in the navigation bar.'),
    },
    {
        id: 'translate-document',
        name: t('Translate document'),
        description: t('You can translate documents by clicking on the translate document menu in the navigation bar.'),
    },
    {
        id: 'translate-history',
        name: t('History'),
        description: t('You can view your translation history by clicking on the history menu in the navigation bar.'),
        pending: true,
    },
    {
        id: 'account-information',
        name: t('Account information'),
        description: t('You can view your account information by clicking on the account menu in the navigation bar.'),
        pending: true,
    },
    {
        id: 'change-plan',
        name: t('Change plan'),
        description: t('You can change your plan by clicking on the change plan menu in the user menu.'),
        pending: true,
    },
    {
        id: 'add-tokens',
        name: t('Add tokens'),
        description: t('You can add tokens to your account by clicking on the add tokens menu in the user menu.'),
        pending: true,
    },
    {
        id: 'payment-history',
        name: t('Payment history'),
        description: t('You can view your payment history by clicking on the payment history menu in the user menu.'),
        pending: true,
    },
]

onMounted(() => {
    nextTick(() => {})
})

const onTourGuide = (group: string) => {
    nextTick(() => {
        tg.value?.start(group)
        //disable scroll html
        document.documentElement.style.overflow = 'hidden'
    })
}
</script>

<template>
    <NuxtLayout name="non-translate">
        <div class="min-h-[calc(100vh-130px)] py-6 pb-24 space-y-4">
            <main class="pt-4 pb-16 lg:pt-4 lg:pb-24 bg-white dark:bg-gray-900">
                <div class="flex justify-between px-4 mx-auto max-w-screen-xl">
                    <article
                        class="mx-auto w-full max-w-screen-md format format-sm sm:format-base lg:format-lg format-primary dark:format-invert"
                    >
                        <header class="mb-4 lg:mb-6 not-format">
                            <address class="flex items-center mb-6 not-italic">
                                <div class="md:inline-flex items-center mr-3 text-sm text-gray-900 dark:text-white">
                                    <img class="mr-4 w-64" src="~/assets/images/tour-guide.svg" alt="Jese Leos" />
                                    <div>
                                        <a rel="author" class="text-base font-bold text-gray-900 dark:text-white">
                                            {{ $t('HOW TO USE DOCTRANSGPT') }}
                                        </a>
                                        <p class="text-sm font-light text-gray-500 dark:text-gray-400">
                                            {{
                                                $t(
                                                    'You can use DoctransGPT to translate your documents, books, articles, etc. to any language you want. You can also use it to generate text for your projects.'
                                                )
                                            }}
                                        </p>
                                    </div>
                                </div>
                            </address>
                        </header>
                        <div class="space-y-3 text-sm text-gray-500 dark:text-gray-400">
                            <h1 class="text-xl">{{ $t('Why Use DocTransGPT for Translation?') }}</h1>
                            <p>
                                {{
                                    $t(
                                        'Google Translate, Microsoft Translator, and DeepL are some of the most popular translation services on the internet. There are also dozens more out there, all offering impressive accuracy, free access, and covering dozens of languages. So why choose DocTransGPT? What makes DocTransGPT a good translation tool?'
                                    )
                                }}
                            </p>
                            <p>
                                {{
                                    $t(
                                        "Well, unlike most popular translation tools, DocTransGPT's interactive nature makes it a standout translation tool. With other translation tools, you provide a text, you get a translation, and that's it. Whether it is the best translation you can get doesn't matter—you're stuck with it. With DocTransGPT, you can interact with the translation model to get the best translation possible."
                                    )
                                }}
                            </p>
                            <p>
                                {{
                                    $t(
                                        'DocTransGPT is also a great translation tool because it is powered by the GPT-3.5 and GPT-4 language model. GPT-3.5 and GPT-4 are the most powerful language model ever created, and it is capable of generating text that is indistinguishable from human-written text. This means that DocTransGPT can generate translations that are more accurate than any other translation tool.'
                                    )
                                }}
                            </p>
                            <p>
                                {{
                                    $t(
                                        "With DocTransGPT, you can customize translations to suit your specific needs and provide feedback on adjustments you'd love to see. For example, you can adjust the tone and style and consider some cultural connotations and regional differences in the meaning of words, something purpose-built translation tools like Google Translate can not do."
                                    )
                                }}
                            </p>
                            <img class="w-full py-6 mx-auto" src="~/assets/images/banner.jpg" alt="banner" />

                            <h1 class="text-xl">{{ $t('How to Translate Languages With DocTransGPT') }}</h1>
                            <p>
                                {{
                                    $t(
                                        "Translating with DocTransGPT is easy. All you need to do is provide the text or the document you want to translate and specify the language you want to translate it to, and DocTransGPT will handle the rest. However, you can customize DocTransGPT's language translation outputs, create unique language translation scenarios, and adjust many more aspects. DocTransGPT is a generative AI tool, after all."
                                    )
                                }}
                            </p>
                            <p>
                                {{ $t('Here are some instructions to help you get the best out of DocTransGPT:') }}
                            </p>

                            <div class="relative overflow-x-auto shadow-md sm:rounded-lg pt-4">
                                <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                                    <tbody>
                                        <tr
                                            v-for="(guide, index) in guides"
                                            class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
                                        >
                                            <th
                                                scope="row"
                                                class="pl-4 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white"
                                            >
                                                {{ index + 1 }}. {{ guide.name }}
                                            </th>
                                            <td class="pl-2 py-4">{{ guide.description }}</td>
                                            <td class="hidden lg:block pl-2 pr-4 py-4 text-right w-[120px]">
                                                <a
                                                    v-if="guide.pending"
                                                    class="font-medium text-gray-600 dark:text-gray-500"
                                                    >{{ $t('Coming soon') }}</a
                                                >
                                                <a
                                                    v-else
                                                    @click="onTourGuide(guide.id)"
                                                    class="font-medium text-primary-600 dark:text-primary-500 hover:underline cursor-pointer"
                                                    >{{ $t('Guide') }}</a
                                                >
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </article>
                </div>
            </main>
        </div>
    </NuxtLayout>
</template>
