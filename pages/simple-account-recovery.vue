<script setup>
import TheLogoTranslate from '~/components/TheLogoTranslate.vue'
import { useAppStore } from '~/stores/app'
const appStore = useAppStore()

definePageMeta({
    layout: false,
})

const router = useRouter()
</script>

<template>
    <NuxtLayout name="blank">
        <div class="flex flex-col items-center justify-center px-6 py-8 mx-auto md:h-screen lg:py-0">
            <div class="flex items-center text-black mb-8 w-full sm:max-w-md dark:text-gray-100">
                <TheLogoTranslate />
            </div>
            <div
                class="w-full p-6 bg-white rounded-lg shadow dark:border md:mt-0 sm:max-w-md dark:bg-gray-800 dark:border-gray-700 sm:p-8 z-50"
            >
                <h1
                    class="mb-1 text-xl font-bold leading-tight tracking-tight text-gray-900 md:text-2xl dark:text-white"
                >
                    Forgot your password?
                </h1>
                <p class="font-light text-gray-500 dark:text-gray-400">
                    Don't fret! Just type in your email and we will send you a code to reset your password!
                </p>
                <form class="mt-4 space-y-4 lg:mt-5 md:space-y-5" action="#">
                    <div>
                        <label for="email" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                            >Your email</label
                        >
                        <input
                            type="email"
                            name="email"
                            id="email"
                            class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-500 placeholder-gray-300 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                            placeholder="<EMAIL>"
                            required=""
                        />
                    </div>
                    <div class="flex items-start">
                        <div class="flex items-center h-5">
                            <input
                                id="terms"
                                aria-describedby="terms"
                                type="checkbox"
                                class="w-4 h-4 border border-gray-300 rounded bg-gray-50 focus:ring-3 focus:ring-primary-300 dark:bg-gray-700 dark:border-gray-600 dark:focus:ring-primary-600 dark:ring-offset-gray-800"
                                required=""
                            />
                        </div>
                        <div class="ml-3 text-sm">
                            <label for="terms" class="font-light text-gray-500 dark:text-gray-300"
                                >I accept the
                                <a
                                    @click="appStore.setShowTermsOfServiceModal(true)"
                                    class="font-medium text-primary-600 hover:underline dark:text-primary-500 cursor-pointer"
                                    >Terms of Service</a
                                ></label
                            >
                        </div>
                    </div>
                    <button
                        type="submit"
                        class="w-full text-white bg-primary-600 hover:bg-primary-700 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
                    >
                        Reset password
                    </button>
                    <p class="text-sm font-light text-gray-500 dark:text-gray-400">
                        <a
                            @click="router.push({ path: '/simple-signin' })"
                            class="flex flex-inline items-center font-medium text-primary-600 hover:underline dark:text-primary-500 cursor-pointer"
                        >
                            <svg
                                class="w-4 h-4 mr-2"
                                aria-hidden="true"
                                fill="none"
                                stroke="currentColor"
                                stroke-width="1.5"
                                viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    d="M10.5 19.5L3 12m0 0l7.5-7.5M3 12h18"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                ></path>
                            </svg>
                            Back to Sign In</a
                        >
                    </p>
                </form>
            </div>
        </div>
    </NuxtLayout>
</template>
