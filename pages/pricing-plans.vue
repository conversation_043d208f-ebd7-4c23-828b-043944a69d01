<template>
  <NuxtLayout name="non-translate">
    <section class="min-h-[calc(100vh-130px)]">
      <div class="mx-auto max-w-screen-xl px-6 lg:px-0">
        <div class="mx-auto w-full text-center mb-4 lg:mb-8 pt-6">
          <h2
            class="text-3xl mb-4 tracking-tight font-extrabold text-gray-900 dark:text-white"
          >
            {{ $t("Pricing Plans") }}
          </h2>

          <p class="flex items-center text-sm text-gray-500 dark:text-gray-400">
            {{
              $t(
                "※ Tokens are pieces of words used for natural language processing (NLP). Depending on the language or difficulty of the text, the number of tokens used will vary."
              )
            }}
            <button id="popover-description-btn" type="button">
              <svg
                class="w-4 h-4 ml-2 text-gray-400 hover:text-gray-500"
                aria-hidden="true"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fill-rule="evenodd"
                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z"
                  clip-rule="evenodd"
                ></path></svg
              ><span class="sr-only">Show information</span>
            </button>
          </p>
          <div
            data-popover
            id="popover-description"
            role="tooltip"
            class="absolute z-10 invisible inline-block text-sm text-gray-500 transition-opacity duration-300 bg-white border border-gray-200 rounded-lg shadow-sm opacity-0 w-72 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400"
          >
            <div class="p-3 space-y-2 text-left">

              <h3 class="font-semibold text-gray-900 dark:text-white">
                {{ $t("For text in English - GPT-4 model") }}
              </h3>
              <p>
                {{ $t("1,000 words ~ 90,000 tokens") }}
              </p>
            </div>
            <div data-popper-arrow></div>
          </div>
        </div>

        <!-- Row 1: Paid Plans + Extend Features -->
        <div class="space-y-8 lg:grid lg:grid-cols-4 lg:space-y-0 lg:space-x-4">
          <!-- Paid Plans (price > 0) -->
          <div
            v-if="loading['getAllProductsAndPlans']"
            v-for="plan in 3"
            class="relative flex flex-col p-0 text-center text-gray-900 border-2 border-white dark:border-gray-800 bg-gray-100 rounded-lg p-4 pt-6 dark:bg-gray-700 dark:text-white hover:shadow-lg !hover:border-primary-600 hover:cursor-pointer hover:scale-105 transition duration-300 ease-in-out"
          >
            <h3 class="mb-0 text-2xl font-semibold">
              <div role="status" class="max-w-sm animate-pulse">
                <div
                  class="h-2.5 bg-gray-500 rounded-full dark:bg-gray-800 w-48 mb-4"
                ></div>
              </div>
            </h3>
            <div class="flex justify-center items-baseline mt-8">
              <div role="status" class="max-w-sm animate-pulse">
                <div
                  class="h-2.5 bg-gray-500 rounded-full dark:bg-gray-800 w-48 mb-4"
                ></div>
              </div>
            </div>
            <div class="flex justify-center items-baseline mb-8 mt-2">
              <span class="mr-1 text-xl font-extrabold text-primary-500">
                <div role="status" class="max-w-sm animate-pulse">
                  <div
                    class="h-2.5 bg-gray-500 rounded-full dark:bg-gray-800 w-48 mb-4"
                  ></div>
                </div>
              </span>
            </div>
            <!-- List -->
            <ul role="list" class="mb-8 space-y-3 text-left text-sm min-h-[250px]">
              <div role="status" class="max-w-sm animate-pulse">
                <div
                  class="h-2.5 bg-gray-500 rounded-full dark:bg-gray-800 w-48 mb-4"
                ></div>
                <div
                  class="h-2 bg-gray-500 rounded-full dark:bg-gray-800 max-w-[360px] mb-2.5"
                ></div>
                <div class="h-2 bg-gray-500 rounded-full dark:bg-gray-800 mb-2.5"></div>
                <div
                  class="h-2 bg-gray-500 rounded-full dark:bg-gray-800 max-w-[330px] mb-2.5"
                ></div>
                <div
                  class="h-2 bg-gray-500 rounded-full dark:bg-gray-800 max-w-[300px] mb-2.5"
                ></div>
                <div
                  class="h-2 bg-gray-500 rounded-full dark:bg-gray-800 max-w-[360px]"
                ></div>
              </div>
            </ul>
          </div>
          <div
            v-else
            v-for="plan in paidPlans"
            class="relative flex flex-col p-0 text-center text-gray-900 border-2 border-white dark:border-gray-800 bg-gray-100 rounded-lg p-4 pt-6 dark:bg-gray-700 dark:text-white hover:shadow-lg !hover:border-primary-600 hover:cursor-pointer hover:scale-105 transition duration-300 ease-in-out"
            :class="{
              '!border-primary-600': user?.current_plan === plan.id,
            }"
            @mouseover="onMouseOverPlan(plan)"
            @mouseleave="onMouseLeavePlan(plan)"
          >
            <div v-if="plan.bonus" class="ribbon ribbon-top-left">
              <span v-if="plan.isHover">+{{ formatUserTokens(plan.tokenBonus) }}</span>
              <span v-else>{{ $t("Bonus top up", { bonus: plan.bonus }) }}</span>
            </div>
            <h3 class="mb-0 text-2xl font-semibold">{{ plan.title }}</h3>
            <div
              class="flex justify-center items-baseline mt-8"
              :class="{ 'scale-125 transition duration-500 ease-in-out': plan.isHover }"
            >
              <span class="mr-2 text-4xl font-extrabold">${{ plan.price }}</span>
              <span class="text-gray-500 dark:text-gray-400">/{{ $t("month") }}</span>
            </div>
            <div class="mb-8 mt-2 flex flex-col !-spcae-y-2">
              <div class="flex justify-center items-baseline">
                <span class="mr-1 text-xl font-extrabold text-primary-500">
                  <number
                    :from="+plan.token"
                    :to="+plan.tokensAfterBonus || plan.token"
                    :format="formatUserTokens"
                    :duration="0.5"
                    easing="Power1.easeOut"
                  />
                </span>
                <span class="text-gray-500 dark:text-gray-400">{{ $t("token") }}</span>
              </div>
              <div class="text-base text-gray-500 font-thin">
                {{ $t("(non-expiring)") }}
              </div>
            </div>
            <!-- List -->
            <ul role="list" class="mb-8 space-y-3 text-left text-sm min-h-[250px]">
              <li v-for="feature in plan.features" class="flex items-center space-x-2">
                <!-- Icon -->
                <svg
                  class="flex-shrink-0 w-5 h-5 text-green-500 dark:text-green-400"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
                <span>{{ feature }}</span>
              </li>
            </ul>
            <a
              v-if="!isLoggedIn && plan.price > 0"
              @click="changePlan(plan)"
              class="w-full cursor-pointer text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800"
            >
              {{ $t("Get started") }}
            </a>
            <a
              v-else-if="isUnverified && plan.price > 0"
              class="w-full cursor-pointer text-white bg-gray-700 hover:bg-gray-800 focus:ring-4 focus:ring-gray-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-gray-600 dark:hover:bg-gray-700 focus:outline-none dark:focus:ring-gray-800"
            >
              {{ $t("Account unverified") }}
            </a>
            <a
              v-else-if="plan.price > 0 && currentSubscriptionPlan !== plan.id"
              @click="changePlan(plan)"
              class="w-full cursor-pointer text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800"
              :class="{
                '!bg-yellow-700 hover:!bg-yellow-800 dark:!bg-yellow-600 dark:hover:!bg-yellow-700':
                  user?.current_plan === plan.id,
              }"
            >
              {{
                user?.current_plan === plan.id
                  ? $t("Extend current plan")
                  : $t("Get started")
              }}
            </a>
            <a
              v-else-if="currentSubscriptionPlan === plan.id"
              class="w-full cursor-pointer text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800"
              :class="{
                '!bg-gray-700 hover:!bg-gray-800 dark:!bg-gray-600 dark:hover:!bg-gray-700':
                  user?.current_plan === plan.id,
              }"
            >
              {{ $t("Current plan") }}
            </a>
          </div>

          <!-- Extend Features GPT-4o-mini Unlimited in Row 1 -->
          <div
            v-if="loading['getAllProductsAndPlans']"
            class="relative flex flex-col p-0 text-center text-gray-900 border-2 border-white dark:border-gray-800 bg-gray-100 rounded-lg p-6 pt-8 dark:bg-gray-700 dark:text-white hover:shadow-lg !hover:border-primary-600 hover:cursor-pointer hover:scale-105 transition duration-300 ease-in-out"
          >
            <div role="status" class="max-w-sm animate-pulse">
              <div class="h-2.5 bg-gray-500 rounded-full dark:bg-gray-800 w-48 mb-4"></div>
              <div class="h-2 bg-gray-500 rounded-full dark:bg-gray-800 max-w-[360px] mb-2.5"></div>
              <div class="h-2 bg-gray-500 rounded-full dark:bg-gray-800 mb-2.5"></div>
            </div>
          </div>
          <div
            v-else-if="extendFeatureProduct"
            class="relative flex flex-col justify-between text-center text-gray-900 border-2 border-white dark:border-gray-800 bg-gradient-to-br from-purple-50 to-blue-50 dark:from-purple-900 dark:to-blue-900 rounded-lg px-4 pb-4 pt-8 dark:text-white hover:shadow-lg !hover:border-purple-600 hover:cursor-pointer hover:scale-105 transition duration-300 ease-in-out"
            :class="{
              '!border-purple-600': userHasExtendFeature,
            }"
          >
            <div class="flex-1">
              <!-- Special Badge -->
              <div class="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <span class="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-4 py-1 rounded-full text-sm font-semibold">
                  {{ $t("Special Offer") }}
                </span>
              </div>

              <h3 class="mb-4 text-2xl font-semibold text-purple-800 dark:text-purple-200">
                {{ $t("GPT-4o-mini Unlimited") }}
              </h3>

              <div class="flex justify-center items-baseline mt-4 mb-6">
                <span class="mr-1 text-4xl font-extrabold text-purple-700 dark:text-purple-300">
                  ${{ extendFeatureProduct.price }}
                </span>
                <span class="text-gray-500 dark:text-gray-400">/{{ $t("year") }}</span>
              </div>

              <!-- Features List -->
              <ul role="list" class="mb-8 space-y-3 text-left text-sm min-h-[250px]">
                <li class="flex items-center space-x-2">
                  <svg class="flex-shrink-0 w-5 h-5 text-purple-500 dark:text-purple-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                  <span>{{ $t("Unlimited GPT-4o-mini usage for 1 year") }}</span>
                </li>
                <li class="flex items-center space-x-2">
                  <svg class="flex-shrink-0 w-5 h-5 text-purple-500 dark:text-purple-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                  <span>{{ $t("Works even with 0 tokens") }}</span>
                </li>
                <li class="flex items-center space-x-2">
                  <svg class="flex-shrink-0 w-5 h-5 text-purple-500 dark:text-purple-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                  <span>{{ $t("No credit lock for document translation") }}</span>
                </li>
                <li class="flex items-center space-x-2">
                  <svg class="flex-shrink-0 w-5 h-5 text-purple-500 dark:text-purple-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                  <span>{{ $t("Valid from purchase date") }}</span>
                </li>
              </ul>
            </div>

            <div class="mt-auto w-full flex">
              <a
                v-if="!isLoggedIn"
                @click="purchaseExtendFeature(extendFeatureProduct)"
                class="w-full cursor-pointer text-white bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 focus:ring-4 focus:ring-purple-300 font-medium rounded-lg text-sm px-5 py-2.5 focus:outline-none dark:focus:ring-purple-800"
              >
                {{ $t("Get started") }}
              </a>
              <a
                v-else-if="isUnverified"
                class="w-full cursor-pointer text-white bg-gray-700 hover:bg-gray-800 focus:ring-4 focus:ring-gray-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-gray-600 dark:hover:bg-gray-700 focus:outline-none dark:focus:ring-gray-800"
              >
                {{ $t("Account unverified") }}
              </a>
              <a
                v-else-if="!userHasExtendFeature"
                @click="purchaseExtendFeature(extendFeatureProduct)"
                class="!w-full flex flex-1 items-center justify-center cursor-pointer text-white bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 focus:ring-4 focus:ring-purple-300 font-medium rounded-lg text-sm px-5 py-2.5 focus:outline-none dark:focus:ring-purple-800"
              >
                {{ $t("Purchase Now") }}
              </a>
              <a
                v-else
                class="!w-full cursor-pointer text-white bg-green-600 hover:bg-green-700 focus:ring-4 focus:ring-green-300 font-medium rounded-lg text-sm px-5 py-2.5 focus:outline-none dark:focus:ring-green-800"
              >
                {{ $t("Activated") }}
              </a>
            </div>
          </div>
        </div>

        <!-- Row 2: Free Plan -->
        <div class="mt-16">
          <div class="mx-auto w-full text-center mb-8">
            <h2 class="text-3xl mb-4 tracking-tight font-extrabold text-gray-900 dark:text-white">
              {{ $t("Free Plan") }}
            </h2>
            <p class="text-lg text-gray-500 dark:text-gray-400">
              {{ $t("Start your translation journey with our free plan") }}
            </p>
          </div>

          <div class="flex justify-center">
            <div
              v-if="loading['getAllProductsAndPlans']"
              class="relative flex flex-col p-0 text-center text-gray-900 border-2 border-white dark:border-gray-800 bg-gray-100 rounded-lg p-4 pt-6 dark:bg-gray-700 dark:text-white hover:shadow-lg !hover:border-primary-600 hover:cursor-pointer hover:scale-105 transition duration-300 ease-in-out max-w-md"
            >
              <h3 class="mb-0 text-2xl font-semibold">
                <div role="status" class="max-w-sm animate-pulse">
                  <div
                    class="h-2.5 bg-gray-500 rounded-full dark:bg-gray-800 w-48 mb-4"
                  ></div>
                </div>
              </h3>
              <div class="flex justify-center items-baseline mt-8">
                <div role="status" class="max-w-sm animate-pulse">
                  <div
                    class="h-2.5 bg-gray-500 rounded-full dark:bg-gray-800 w-48 mb-4"
                  ></div>
                </div>
              </div>
              <div class="flex justify-center items-baseline mb-8 mt-2">
                <span class="mr-1 text-xl font-extrabold text-primary-500">
                  <div role="status" class="max-w-sm animate-pulse">
                    <div
                      class="h-2.5 bg-gray-500 rounded-full dark:bg-gray-800 w-48 mb-4"
                    ></div>
                  </div>
                </span>
              </div>
              <!-- List -->
              <ul role="list" class="mb-8 space-y-3 text-left text-sm min-h-[250px]">
                <div role="status" class="max-w-sm animate-pulse">
                  <div
                    class="h-2.5 bg-gray-500 rounded-full dark:bg-gray-800 w-48 mb-4"
                  ></div>
                  <div
                    class="h-2 bg-gray-500 rounded-full dark:bg-gray-800 max-w-[360px] mb-2.5"
                  ></div>
                  <div class="h-2 bg-gray-500 rounded-full dark:bg-gray-800 mb-2.5"></div>
                  <div
                    class="h-2 bg-gray-500 rounded-full dark:bg-gray-800 max-w-[330px] mb-2.5"
                  ></div>
                  <div
                    class="h-2 bg-gray-500 rounded-full dark:bg-gray-800 max-w-[300px] mb-2.5"
                  ></div>
                  <div
                    class="h-2 bg-gray-500 rounded-full dark:bg-gray-800 max-w-[360px]"
                  ></div>
                </div>
              </ul>
            </div>
            <div
              v-else-if="freePlan"
              class="relative flex flex-col p-0 text-center text-gray-900 border-2 border-white dark:border-gray-800 bg-gray-100 rounded-lg p-4 pt-6 dark:bg-gray-700 dark:text-white hover:shadow-lg !hover:border-primary-600 hover:cursor-pointer hover:scale-105 transition duration-300 ease-in-out max-w-md"
              :class="{
                '!border-primary-600': user?.current_plan === freePlan.id,
              }"
              @mouseover="onMouseOverPlan(freePlan)"
              @mouseleave="onMouseLeavePlan(freePlan)"
            >
              <div v-if="freePlan.bonus" class="ribbon ribbon-top-left">
                <span v-if="freePlan.isHover">+{{ formatUserTokens(freePlan.tokenBonus) }}</span>
                <span v-else>{{ $t("Bonus top up", { bonus: freePlan.bonus }) }}</span>
              </div>
              <h3 class="mb-0 text-2xl font-semibold">{{ freePlan.title }}</h3>
              <div
                class="flex justify-center items-baseline mt-8"
                :class="{ 'scale-125 transition duration-500 ease-in-out': freePlan.isHover }"
              >
                <span class="mr-2 text-4xl font-extrabold">${{ freePlan.price }}</span>
                <span class="text-gray-500 dark:text-gray-400">/{{ $t("month") }}</span>
              </div>
              <div class="mb-8 mt-2 flex flex-col !-spcae-y-2">
                <div class="flex justify-center items-baseline">
                  <span class="mr-1 text-xl font-extrabold text-primary-500">
                    <number
                      :from="+freePlan.token"
                      :to="+freePlan.tokensAfterBonus || freePlan.token"
                      :format="formatUserTokens"
                      :duration="0.5"
                      easing="Power1.easeOut"
                    />
                  </span>
                  <span class="text-gray-500 dark:text-gray-400">{{ $t("token") }}</span>
                </div>
                <div class="text-base text-gray-500 font-thin">
                  {{ $t("(non-expiring)") }}
                </div>
              </div>
              <!-- List -->
              <ul role="list" class="mb-8 space-y-3 text-left text-sm min-h-[250px]">
                <li v-for="feature in freePlan.features" class="flex items-center space-x-2">
                  <!-- Icon -->
                  <svg
                    class="flex-shrink-0 w-5 h-5 text-green-500 dark:text-green-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                  <span>{{ feature }}</span>
                </li>
              </ul>
              <a
                v-if="!isLoggedIn"
                @click="changePlan(freePlan)"
                class="w-full cursor-pointer text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800"
              >
                {{ $t("Get started") }}
              </a>
              <a
                v-else-if="isUnverified"
                class="w-full cursor-pointer text-white bg-gray-700 hover:bg-gray-800 focus:ring-4 focus:ring-gray-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-gray-600 dark:hover:bg-gray-700 focus:outline-none dark:focus:ring-gray-800"
              >
                {{ $t("Account unverified") }}
              </a>
              <a
                v-else-if="currentSubscriptionPlan !== freePlan.id"
                @click="changePlan(freePlan)"
                class="w-full cursor-pointer text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800"
                :class="{
                  '!bg-yellow-700 hover:!bg-yellow-800 dark:!bg-yellow-600 dark:hover:!bg-yellow-700':
                    user?.current_plan === freePlan.id,
                }"
              >
                {{
                  user?.current_plan === freePlan.id
                    ? $t("Extend current plan")
                    : $t("Get started")
                }}
              </a>
              <a
                v-else-if="currentSubscriptionPlan === freePlan.id"
                class="w-full cursor-pointer text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800"
                :class="{
                  '!bg-gray-700 hover:!bg-gray-800 dark:!bg-gray-600 dark:hover:!bg-gray-700':
                    user?.current_plan === freePlan.id,
                }"
              >
                {{ $t("Current plan") }}
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  </NuxtLayout>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from "vue";
import { storeToRefs } from "pinia";
import { useAuthStore } from "~~/stores/auth";
import { usePaymentsStore } from "~~/stores/payments";
import { useI18n } from "vue-i18n";
import { loadScript } from "@paypal/paypal-js";
import PaymentModal from "~/base-components/PaymentModal.vue";
import { ModalsContainer, useModal } from "vue-final-modal";
import { Popover } from "flowbite";
import type { PopoverOptions, PopoverInterface } from "flowbite";
const popover = ref<PopoverInterface | null>(null);
const paymentStore = usePaymentsStore();
const { planList, loading } = storeToRefs(paymentStore);
onMounted(async () => {
  // set the popover content element
  const $targetEl: HTMLElement = document.getElementById("popover-description");

  // set the element that trigger the popover using hover or click
  const $triggerEl: HTMLElement = document.getElementById("popover-description-btn");

  // options with default values
  const options: PopoverOptions = {
    placement: "bottom",
    triggerType: "hover",
    offset: 10,
    onHide: () => {
      console.log("popover is shown");
    },
    onShow: () => {
      console.log("popover is hidden");
    },
    onToggle: () => {
      console.log("popover is toggled");
    },
  };

  // create the popover instance
  popover.value = new Popover($targetEl, $triggerEl, options);

  await paymentStore.getAllProductsAndPlans();

  // Filter extend feature product (BP0001)
  const extendProduct = paymentStore.products.find(product => product.id === 'BP0001');
  if (extendProduct) {
    extendFeatureProduct.value = {
      ...extendProduct,
      product_id: extendProduct.id,
      price: +extendProduct.price_divide_100,
    };
  }

  // Check if user has extend feature
  if (user.value?.user_plan?.product?.id === 'BP0001') {
    userHasExtendFeature.value = true;
  }

  plans.value = planList.value?.map((plan) => {
    const features = plan.usable_gpt_versions?.map((version: string) =>
      t(version.toUpperCase())
    );
    // if (plan.id !== 'FP0001') {
    //     features.push(t('GPT-4'))
    // }
    if (plan.max_monthly_total_file === 999999999999) {
      features.push(t("Unlimited document translation"));
    } else {
      features.push(t("documents/month", { num: +plan.max_monthly_total_file }));
    }

    // convert bytes to mb
    const max_file_size = Math.round(+plan.max_file_size / 1024 / 1024);
    features.push(t("Up to size per document", { size: max_file_size + "MB" }));
    if (plan.id !== "FP0001") {
      features.push(t("No Ads"));
    }
    return {
      ...plan,
      title: t(plan.id),
      features,
    };
  });

  // Separate paid plans (price > 0) and free plan (price = 0)
  if (plans.value) {
    paidPlans.value = plans.value.filter(plan => plan.price > 0);
    freePlan.value = plans.value.find(plan => plan.price === 0);
  }
});

definePageMeta({
  layout: false,
});
const authStore = useAuthStore();
const { isUnverified, user, isLoggedIn, currentSubscriptionPlan } = storeToRefs(
  authStore
);
const { t } = useI18n({ useScope: "global" });
const router = useRouter();
const { open, close, options } = useModal({
  component: PaymentModal,
  attrs: {
    onClose() {
      close();
    },
    onSubmit() {
      close();
    },
  },
});

const plans = ref([]);
const paidPlans = ref([]);
const freePlan = ref(null);
const extendFeatureProduct = ref(null);
const userHasExtendFeature = ref(false);

const changePlan = (plan) => {
  if (!isLoggedIn.value) {
    router.push("/signin");
    return;
  }
  options.attrs.type = "change-plan";
  options.attrs.plan = plan;
  options.attrs.id = plan.product_id;
  options.attrs.quantity = 1;
  open();
};

const purchaseExtendFeature = (product) => {
  if (!isLoggedIn.value) {
    router.push("/signin");
    return;
  }
  options.attrs.type = "extend-feature";
  options.attrs.plan = product;
  options.attrs.id = product.product_id;
  options.attrs.quantity = 1;
  open();
};

const onMouseOverPlan = (plan) => {
  plan.isHover = true;
  plan.tokenBonus = Math.round((plan.token * plan.bonus) / 100);
  plan.tokensAfterBonus = +plan.token + Math.round((plan.token * plan.bonus) / 100);
};

const onMouseLeavePlan = (plan) => {
  plan.isHover = false;
  plan.tokensAfterBonus = +plan.token;
};

const formatUserTokens = (number) => {
  if (!number) return 0;
  return new Intl.NumberFormat().format(number?.toFixed(0));
};
</script>
