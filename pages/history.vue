<script setup lang="ts">
import { onMounted, watch } from 'vue'
import { storeToRefs } from 'pinia'
import HistoryMenus from '~/components/HistoryMenus.vue'
import HistoryList from '~/components/HistoryList.vue'
import HistoryDeleteButton from '~/components/HistoryDeleteButton.vue'
import MobileBottomMenu from '~/components/MobileBottomMenu.vue'
import SignInSignUpRequire from '~/components/SignInSignUpRequire.vue'
import { useAuthStore } from '~~/stores/auth'
import { useTranslateStore } from '~~/stores/translate'
import { useAppStore } from '~~/stores/app'
import { useI18n } from 'vue-i18n'
const { t } = useI18n({ useScope: 'global' })
const translateStore = useTranslateStore()
const authStore = useAuthStore()
const { isLoggedIn, isLoggingIn, showAds } = storeToRefs(authStore)
const appStore = useAppStore()
const { tg, showNotificationsDropdown } = storeToRefs(appStore)
onMounted(() => {
    translateStore.initSelectedLanguages()

    nextTick(async () => {
        tg.value?.addSteps([
            {
                order: 3,
                group: 'translate-history',
                target: '#translation-history-menu',
                content: t('tour_guide.translation_history_menu'),
                title: t('Translation history menu'),
            },
            {
                order: 4,
                group: 'translate-history',
                target: '#btn-toggle-select-language-from',
                content: t('tour_guide.click_search_more_from_language'),
                title: t('Search more languages'),
            },
        ])
        // tg.value.start('translate-history')
        // tg.value.visitStep(2)
        //check localstorage to show tour guide
        // const isShowTourGuide = localStorage.getItem('historyTourGuide')
        // if (!isShowTourGuide) {
        //     tg.value.visitStep(1)
        //     tg.value?.start('translate-history')
        //     // localStorage.setItem('historyTourGuide', 'true')
        // }

        let script = document.createElement('script')
        script.innerHTML = `(function(w,q){w[q]=w[q]||[];w[q].push(["_mgc.load"])})(
        window,"_mgq"
    );`
        document.body.appendChild(script)
    })
})

definePageMeta({
    layout: false,
})
</script>

<template>
    <NuxtLayout name="non-translate">
        <div v-show="!isLoggingIn" class="relative min-h-[calc(100vh-130px)] py-6 pb-24 space-y-4">
            <div class="bg-gray-50 dark:bg-gray-900 sticky top-20 z-20 py-3">
                <HistoryMenus id="translation-history-menu" />
            </div>
            <div v-if="!isLoggedIn" class="max-w-lg mx-auto p-4 md:p-0">
                <SignInSignUpRequire
                    class="p-14"
                    :title="$t('Want to see your translation history?')"
                    :message="$t('You can see your translation history here. Sign in or sign up to see your history.')"
                />
                <div v-if="showAds" class="pt-2">
                    <div data-type="_mgwidget" data-widget-id="1603218"></div>
                </div>
            </div>
            <template v-else>
                <div class="md:max-w-screen-md mx-auto flex justify-between flex-inline items-center">
                    <div class="pl-4 md:pl-8 font-thin text-base text-gray-600 dark:text-gray-300">
                        {{ $t('All history after 30 days will be deleted automatically.') }}
                    </div>
                    <div class="hidden md:block">
                        <HistoryDeleteButton />
                    </div>
                </div>

                <HistoryList class="md:max-w-screen-md mx-auto md:px-0 pl-6 pr-4" />
                <div
                    v-if="showAds"
                    class="hidden md:flex fixed bottom-0 justify-center items-center left-0 w-full h-[85px]"
                >
                    <div data-type="_mgwidget" data-widget-id="1603203"></div>
                </div>
            </template>
        </div>
        <MobileBottomMenu lite>
            <HistoryDeleteButton />
        </MobileBottomMenu>
    </NuxtLayout>
</template>
