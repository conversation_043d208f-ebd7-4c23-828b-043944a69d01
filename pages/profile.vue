<script setup lang="ts">
definePageMeta({
    layout: false,
})

import ProfileCard from '~/components/ProfileCard.vue'

const route = useRoute()
const hideLeftMenu = computed(() => route.path === 'pricing-plans')
</script>

<template>
    <NuxtLayout name="non-translate">
        <div class="flex-1 min-h-[calc(100vh-130px)] pt-2 md:pt-6">
            <div class="grid grid-cols-3 h-full">
                <div v-if="!hideLeftMenu" class="hidden lg:block pt-10">
                    <ProfileCard class="md:sticky top-10" :canCancelSubscription="true"/>
                </div>
                <div
                    class="col-span-3 lg:col-span-2 md:border dark:border-gray-600 bg-white dark:bg-gray-800 dark:text-gray-300"
                    :class="{
                        'lg:col-span-3': hideLeftMenu,
                    }"
                >
                    <NuxtPage />
                </div>
            </div>
        </div>
    </NuxtLayout>
</template>
