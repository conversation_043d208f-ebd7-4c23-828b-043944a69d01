<script setup>
import TheLogoTranslate from '~/components/TheLogoTranslate.vue'
import { useAppStore } from '~/stores/app'
import { useAuthStore } from '~/stores/auth'
import Loading from 'vue-loading-overlay'
import { useI18n } from 'vue-i18n'
import * as validators from '@vuelidate/validators'
import { reactive, onMounted, onUnmounted } from 'vue'
import { useVuelidate } from '@vuelidate/core'
import { storeToRefs } from 'pinia'

const router = useRouter()
const i18n = useI18n()
const { createI18nMessage } = validators
const withI18nMessage = createI18nMessage({ t: i18n.t.bind(i18n) })
const required = withI18nMessage(validators.required)
const email = withI18nMessage(validators.email)

const formForgotPassword = reactive({
    email: ''
})
const formRules = computed(() => ({
    email: { required, email }
}))
const v$ = useVuelidate(formRules, formForgotPassword)
const appStore = useAppStore()
const authStore = useAuthStore()
const { forgotPasswordError,  } = storeToRefs(authStore)
const { acceptedTermsOfService } = storeToRefs(appStore)

definePageMeta({
    layout: false
})

onMounted(() => {
    forgotPasswordError.value = null
})

onUnmounted(() => {
    acceptedTermsOfService.value = false
})

const isLoading = ref(false)
const isSuccess = ref(false)
const isError = ref(false)
const onSubmit = async () => {
    const result = await v$.value.$validate()
    if (!result) {
        return
    }

    isLoading.value = true
    const res = await authStore.forgotPassword({
        email: formForgotPassword.email
    })
    isLoading.value = false
    if (res === true) {
        isSuccess.value = true
    }
}
</script>

<template>
    <NuxtLayout name='animation'>
        {{ acceptedTermsOfService }}
        <div class='flex flex-col items-center justify-center px-6 py-8 mx-auto md:h-screen lg:py-0'>
            <div class='flex items-center text-white mb-8 w-full sm:max-w-md'>
                <TheLogoTranslate />
            </div>
            <div
                class='w-full p-6 bg-white rounded-lg shadow dark:border md:mt-0 sm:max-w-md dark:bg-gray-800 dark:border-gray-700 sm:p-8 z-20'
            >
                <loading height='35' v-model:active='isLoading' :is-full-page='false' />
                <h1
                    class='mb-1 text-xl font-bold leading-tight tracking-tight text-gray-900 md:text-2xl dark:text-white'
                >
                    {{ $t('Forgot your password?') }}
                </h1>
                <p class='font-light text-gray-500 dark:text-gray-400'>
                    {{ $t('Don\'t fret! Just type in your email and we will send you a code to reset your password!') }}
                </p>
                <form @submit.prevent='onSubmit' class='mt-4 space-y-4 lg:mt-5 md:space-y-5'>
                    <div>
                        <label for='email' class='block mb-2 text-sm font-medium text-gray-900 dark:text-white'
                               :class="{ 'text-red-700 dark:text-red-500': v$.email.$error || forgotPasswordError}">
                            {{ $t('Your email') }}
                        </label>
                        <input
                            v-model='formForgotPassword.email'
                            type='email'
                            name='email'
                            id='email'
                            class='bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-500 placeholder-gray-300 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500'
                            placeholder='<EMAIL>'
                            required=''
                            :class="{
                                    'bg-red-50 border border-red-500 text-red-900 placeholder-red-700 focus:ring-red-500 dark:bg-gray-700 focus:border-red-500 dark:text-red-500 dark:placeholder-red-500 dark:border-red-500':
                                        v$.email.$error || forgotPasswordError,
                                }"
                        />
                        <p v-if="v$.email.$errors || forgotPasswordError" class="mt-2 text-sm text-red-600 dark:text-red-500">
                            {{ v$.email.$errors[0]?.$message || i18n.t(forgotPasswordError?.detail?.error_message || "" )}}
                        </p>
                    </div>
                    <div class='flex items-start'>
                        <div class='flex items-center h-5'>
                            <input
                                id='terms'
                                aria-describedby='terms'
                                type='checkbox'
                                class='w-4 h-4 text-primary-600 border border-gray-300 rounded bg-gray-50 focus:ring-3 focus:ring-primary-300 dark:bg-gray-700 dark:border-gray-600 dark:focus:ring-primary-600 dark:ring-offset-gray-800'
                                required
                                v-model="acceptedTermsOfService"
                            />
                        </div>
                        <div class='ml-3 text-sm'>
                            <label for='terms' class='font-light text-gray-500 dark:text-gray-300'>
                                <i18n-t keypath='accept-terms' tag='span'>
                                    <a
                                        @click='appStore.setShowTermsOfServiceModal(true)'
                                        class='font-medium text-primary-600 hover:underline dark:text-primary-500 cursor-pointer'
                                    >
                                        {{ $t('Terms of Service') }}
                                    </a>
                                </i18n-t>
                            </label>
                        </div>
                    </div>
                    <div v-if="isSuccess" id="alert-3" class="flex p-4 mb-4 text-sm text-green-800 border border-green-300 rounded-lg bg-green-50 dark:bg-gray-800 dark:text-green-400 dark:border-green-800" role="alert">
                        <svg aria-hidden="true" class="flex-shrink-0 w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path></svg>
                        <span class="sr-only">Info</span>
                        <div class="ml-3 text-sm font-medium">
                            {{ $t('Password recovery email sent!') }}
                        </div>
                        <button @click="isSuccess = false" type="button" class="ml-auto -mx-1.5 -my-1.5 bg-green-50 text-green-500 rounded-lg focus:ring-2 focus:ring-green-400 p-1.5 hover:bg-green-200 inline-flex h-8 w-8 dark:bg-gray-800 dark:text-green-400 dark:hover:bg-gray-700" data-dismiss-target="#alert-3" aria-label="Close">
                            <span class="sr-only">Close</span>
                            <svg aria-hidden="true" class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
                        </button>
                    </div>
                    <button
                        :disabled='isLoading'
                        type='submit'
                        class='w-full text-white bg-primary-600 hover:bg-primary-700 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800'
                    >
                        {{ $t('Send me a code') }}
                    </button>
                    <p class='text-sm font-light text-gray-500 dark:text-gray-400'>
                        <a
                            @click="router.push({ path: '/signin' })"
                            class='flex flex-inline items-center font-medium text-primary-600 hover:underline dark:text-primary-500 cursor-pointer'
                        >
                            <svg
                                class='w-4 h-4 mr-2'
                                aria-hidden='true'
                                fill='none'
                                stroke='currentColor'
                                stroke-width='1.5'
                                viewBox='0 0 24 24'
                                xmlns='http://www.w3.org/2000/svg'
                            >
                                <path
                                    d='M10.5 19.5L3 12m0 0l7.5-7.5M3 12h18'
                                    stroke-linecap='round'
                                    stroke-linejoin='round'
                                ></path>
                            </svg>
                            {{ $t('Back to Sign In') }}
                        </a>
                    </p>
                </form>
            </div>
        </div>
    </NuxtLayout>
</template>
