<script setup lang="ts">
definePageMeta({
    layout: false,
})
</script>

<template>
    <NuxtLayout name="non-translate">
        <div class="min-h-[calc(100vh-130px)] py-6 pb-24 space-y-4">
            <main class="pt-4 pb-16 lg:pt-4 lg:pb-24 bg-white dark:bg-gray-900">
                <div class="flex justify-between px-4 mx-auto max-w-screen-xl">
                    <article
                        class="mx-auto w-full max-w-screen-md format format-sm sm:format-base lg:format-lg format-blue dark:format-invert"
                    >
                        <header class="mb-4 lg:mb-6 not-format">
                            <address class="flex items-center mb-6 not-italic">
                                <div class="md:inline-flex items-center mr-3 text-sm text-gray-900 dark:text-white">
                                    <img class="mr-4 w-64" src="~/assets/images/privacy.jpg" alt="Je<PERSON>" />
                                    <div>
                                        <a
                                            href="#"
                                            rel="author"
                                            class="text-base font-bold text-gray-900 dark:text-white"
                                        >
                                            {{ $t('DOCTRANSGPT PRIVACY POLICY') }}
                                        </a>
                                        <p class="text-sm font-light text-gray-500 dark:text-gray-400">
                                            {{
                                                $t(
                                                    'This Privacy Policy is meant to help you understand what information we collect, why we collect it, and how you can update, manage, export, and delete your information.'
                                                )
                                            }}
                                        </p>
                                    </div>
                                </div>
                            </address>
                        </header>
                        <div
                            class="space-y-3 text-sm leading-relaxed text-gray-500 dark:text-gray-400"
                            v-html="$t('privacy-policy-content')"
                        ></div>
                    </article>
                </div>
            </main>
        </div>
    </NuxtLayout>
</template>

<style>
ol {
    list-style-type: decimal;
    padding-left: 20px;
}
</style>
