<script setup lang="ts"></script>

<template>
    <section class="relative min-h-[calc(100vh-70px)] h-full bg-white dark:bg-gray-800">
        <div class="max-w-screen-xl h-full flex flex-col items-center justify-center">
            <div class="mb-6">
                <svg
                    class="w-20 h-20 text-primary-600 dark:text-primary-600"
                    aria-hidden="true"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                >
                    <path
                        d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z"
                    />
                </svg>
            </div>
            <div class="text-4xl text-primary-600 mb-1">{{ $t('Thank You!') }}</div>
            <div>{{ $t('Your Payment is Successfull') }}</div>
            <div v-if="subscribe" class="text-primary-600">
                {{ $t('Please wait while we are processing your subcription...') }}
            </div>
            <div class="text-center text-sm mt-8 dark:text-gray-400">
                {{ $t('You will be redirected to Account Information page after few seconds') }}
            </div>
            <button
                @click="router.push({ path: '/' })"
                type="button"
                class="mt-6 text-white bg-primary-700 hover:bg-primary-800 focus:outline-none focus:ring-4 focus:ring-primary-300 font-medium rounded-full text-sm px-16 py-2.5 text-centerdark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
            >
                {{ $t('Home') }}
            </button>
        </div>
        <div v-if="showFirework" v-for="i in 3" class="firework"></div>
    </section>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useAuthStore } from '~/stores/auth'

const authStore = useAuthStore()
let fireworkTimer: any = null
const router = useRouter()
const route = useRoute()
const { subscribe } = route.query
const showFirework = ref(true)
onMounted(() => {
    fireworkTimer = setTimeout(() => {
        showFirework.value = false

        router.push('/profile/')
    }, 5500)

    authStore.syncUserTokenInfo()
})
</script>
