<template>
  <section class="bg-white dark:bg-gray-800">
    <div class="relative">
      <div
        class="text-white w-full bg-gradient-1 rounded-b-3xl h-40 p-6 pb-14 flex flex-col justify-end"
      >
        <div class="text-sm">{{ $t("Available tokens") }}</div>

        <div class="text-3xl flex items-center space-x-1">
          <div>
            <!-- TODO: hardcode token for free plan -->
            <number
              :from="0"
              :to="user?.tokens"
              :format="formatUserTokens"
              :duration="2"
              easing="Power1.easeOut"
            />
            <small class="text-xs">{{ $t("tokens") }} {{$t('(non-expiring)')}}</small>
          </div>
        </div>
      </div>
      <div
        class="bg-gray-50 dark:bg-gray-700 rounded-3xl mx-4 -mt-10 mb-6 px-0 py-6 flex flex-col space-y-4"
      >
        <div class="space-y-2">
          <div class="px-4">{{ $t("Quick top up") }}</div>
          <div
            ref="scrollContainer"
            class="flex flex-nowrap overflow-auto space-x-6 scrollbar-thin px-4 py-2"
          >
            <div
              v-for="(topupValue, index) in quickTopUpList"
              @click="onQuickTopup(topupValue)"
              class="space-y-2 group cursor-pointer"
            >
              <div
                class="relative group w-32 px-4 py-6 text-center rounded-2xl bg-gradient-dark border dark:border-gray-900 dark:hover:border-yellow-100 hover:scale-105"
              >
                <div
                  class="text-2xl hidden group-hover:block text-yellow-500 dark:text-yellow-300"
                >
                  +
                  <number
                    :key="index"
                    :from="topupValue.tokens"
                    :to="topupValue.tokens * (1 + bonus / 100)"
                    :format="$abbreviatedUnit"
                    :duration="1"
                  />
                </div>
                <div class="text-2xl group-hover:hidden">
                  + {{ $abbreviatedUnit(topupValue.tokens) }}
                </div>
                <small
                  class="text-xs group-hover:text-yellow-500 dark:group-hover:text-yellow-300"
                  >{{ $t("tokens") }}</small
                >
                <div
                  class="text-xs group-hover:text-yellow-500 dark:group-hover:text-yellow-300"
                >
                  {{ $t("(non-expiring)") }}
                </div>
                <div class="text-center mt-2 h-6">
                  <span
                    v-if="bonus > 0"
                    class="group-hover:hidden bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-gray-700 dark:text-yellow-300 border border-yellow-300"
                    >+{{ bonus }}%</span
                  >
                  <svg
                    class="hidden group-hover:block w-5 h-5 text-yellow-500 dark:text-yellow-300 mx-auto"
                    aria-hidden="true"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 10 14"
                  >
                    <path
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M5 1v12m0 0 4-4m-4 4L1 9"
                    />
                  </svg>
                </div>
              </div>
              <div
                class="text-center text-lg group-hover:scale-125 group-hover:text-yellow-500 dark:group-hover:text-yellow-300"
              >
                {{ topupValue.price }}$
              </div>
            </div>
          </div>
        </div>
        <hr class="h-px my-8 bg-gray-200 border-0 dark:bg-gray-600" />
        <div class="p-4 space-y-4">
          <div class="">{{ $t("Custom top up") }}</div>
          <div>
            <div class="grid grid-cols-5 md:grid-cols-3 items-center">
              <div class="text-right">
                <button
                  @click="topUpTokens -= tokenUnit"
                  type="button"
                  class="disable-dbl-tap-zoom text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-full text-sm p-3.5 text-center inline-flex items-center mr-2 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
                >
                  <svg
                    class="w-5 h-5 text-gray-200 dark:text-white"
                    aria-hidden="true"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 18 2"
                  >
                    <path
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M1 1h16"
                    />
                  </svg>
                  <span class="sr-only">Icon description</span>
                </button>
              </div>
              <div class="text-center col-span-3 md:col-span-1">
                <div class="text-yellow-800 dark:text-yellow-300 text-left w-fit mx-auto">
                  <div class="text-4xl">
                    {{ $abbreviatedUnit(topUpTokens) }}
                    <small class="text-xs">{{ $t("tokens") }}</small>
                  </div>
                  <div class="text-xl dark:text-white">
                    ~ {{ topUpAmount }}
                    <small class="text-xs">{{ $t("$") }}</small>
                  </div>
                </div>
              </div>
              <div>
                <button
                  @click="topUpTokens += tokenUnit"
                  type="button"
                  class="disable-dbl-tap-zoom text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-full text-sm p-3.5 text-center inline-flex items-center mr-2 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
                >
                  <svg
                    class="w-5 h-5 text-gray-200 dark:text-white"
                    aria-hidden="true"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 18 18"
                  >
                    <path
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M9 1v16M1 9h16"
                    />
                  </svg>
                  <span class="sr-only">Icon description</span>
                </button>
              </div>
            </div>
          </div>
          <div class="w-full md:w-1/2 mx-auto px-2 space-y-5">
            <slider
              v-model="topUpTokens"
              :min="tokenUnit"
              :max="tokenUnit * 300"
              :step="tokenUnit"
              height="17"
              color="#3a9284"
              track-color="#FFFFFF"
            />
            <button
              @click="onCustomTopup"
              type="button"
              class="group w-full text-gray-900 bg-[#F7BE38] hover:bg-[#F7BE38]/90 focus:ring-4 focus:outline-none focus:ring-[#F7BE38]/50 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex justify-center items-center dark:focus:ring-[#F7BE38]/50 mr-2 mb-2"
            >
              <svg
                class="w-4 h-4 mr-2 -ml-1"
                aria-hidden="true"
                focusable="false"
                data-prefix="fab"
                data-icon="paypal"
                role="img"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 384 512"
              >
                <path
                  fill="currentColor"
                  d="M111.4 295.9c-3.5 19.2-17.4 108.7-21.5 134-.3 1.8-1 2.5-3 2.5H12.3c-7.6 0-13.1-6.6-12.1-13.9L58.8 46.6c1.5-9.6 10.1-16.9 20-16.9 152.3 0 165.1-3.7 204 11.4 60.1 23.3 65.6 79.5 44 140.3-21.5 62.6-72.5 89.5-140.1 90.3-43.4 .7-69.5-7-75.3 24.2zM357.1 152c-1.8-1.3-2.5-1.8-3 1.3-2 11.4-5.1 22.5-8.8 33.6-39.9 113.8-150.5 103.9-204.5 103.9-6.1 0-10.1 3.3-10.9 9.4-22.6 140.4-27.1 169.7-27.1 169.7-1 7.1 3.5 12.9 10.6 12.9h63.5c8.6 0 15.7-6.3 17.4-14.9 .7-5.4-1.1 6.1 14.4-91.3 4.6-22 14.3-19.7 29.3-19.7 71 0 126.4-28.8 142.9-112.3 6.5-34.8 4.6-71.4-23.8-92.6z"
                ></path>
              </svg>
              <div class="group-hover:hidden">
                {{ $t("Top up now") }} <span v-if="bonus > 0">(+{{ bonus }}%)</span>
              </div>
              <div class="hidden group-hover:flex flex-inline items-center">
                <div>
                  {{ $abbreviatedUnit(topUpTokens * (1 + bonus / 100)) }}
                  {{ $t("tokens") }}
                </div>

                <div>({{ topUpAmount }}{{ $t("$") }})</div>
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import { storeToRefs } from "pinia";
import { useAuthStore } from "~~/stores/auth";
import { usePaymentsStore, TOKEN_PROD_ID } from "~~/stores/payments";
import slider from "vue3-slider";
import PaymentModal from "~/base-components/PaymentModal.vue";
import { ModalsContainer, useModal } from "vue-final-modal";

const { $abbreviatedUnit } = useNuxtApp();
const { open, close, options } = useModal({
  component: PaymentModal,
  attrs: {
    onClose() {
      close();
    },
    onSubmit() {
      close();
    },
  },
});

const authStore = useAuthStore();
const { isLoggingIn, user, isUsingFreePlan } = storeToRefs(authStore);

const paymentsStore = usePaymentsStore();
const {
  quickTopUpList,
  bonus,
  topUpTokens,
  topUpHistory,
  tokenUnit,
  topUpAmount,
} = storeToRefs(paymentsStore);

const formatUserTokens = (number) => {
  return new Intl.NumberFormat().format(number.toFixed(0));
};
const scrollContainer = ref(null);
const scrollX = (e) => {
  scrollContainer.value.scrollLeft += e.deltaY * 1.5;
};

const onQuickTopup = (topupValue) => {
  topUpTokens.value = topupValue.tokens;
  options.attrs.tokens = topupValue.tokens;
  options.attrs.price = topupValue.price;
  options.attrs.quantity = topupValue.quantity;
  options.attrs.id = TOKEN_PROD_ID;
  options.attrs.bonus = bonus;
  open();
};

const onCustomTopup = () => {
  options.attrs.tokens = topUpTokens.value;
  options.attrs.price = topUpAmount.value;
  options.attrs.bonus = bonus;
  options.attrs.quantity = topUpTokens.value / tokenUnit.value;
  options.attrs.id = TOKEN_PROD_ID;
  open();
};
</script>
