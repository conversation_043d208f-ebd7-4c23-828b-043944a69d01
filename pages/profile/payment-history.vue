<template>
    <div v-if="user" class="space-y-3 p-0 md:p-4">
        <div class="p-6">
            <div class="">
                <h4 class="font-semibold text-lg">
                    {{ $t('Payment history') }}
                </h4>
                <hr class="h-px mt-2 mb-5 bg-gray-200 border-0 dark:bg-gray-700" />
                <div class="px-6 py-4">
                    <loading loader="bars" height="35" v-model:active="loading['getOrderHistories']" :is-full-page="true" />
                    <div
                        v-if="paymentHistories.length === 0"
                        class="block p-5 text-sm text-center text-gray-700 dark:text-white"
                    >
                        <svg
                            class="w-10 h-10 text-gray-800 dark:text-white mx-auto mb-4"
                            aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 20 20"
                        >
                            <path
                                stroke="currentColor"
                                stroke-linejoin="round"
                                stroke-width="1"
                                d="M10 6v4l3.276 3.276M19 10a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                            />
                        </svg>
                        {{ $t('No top up history found.') }}
                    </div>
                    <ol class="relative border-l border-gray-200 dark:border-gray-700">
                        <component
                            v-for="historyRow in paymentHistories"
                            :key="historyRow.id"
                            :is="paymentHistoryItemComponents[historyRow.order_product.type]"
                            v-bind="historyRow"
                        />
                    </ol>
                    <div class="flex flex-inline w-max mx-auto">
                        <!-- Previous Button -->
                        <a
                            v-if="fetchOptions.page > 1"
                            @click="previousPage"
                            href="#"
                            class="inline-flex items-center px-4 py-2 mr-3 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white"
                        >
                            <svg
                                aria-hidden="true"
                                class="w-5 h-5 mr-2"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    fill-rule="evenodd"
                                    d="M7.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l2.293 2.293a1 1 0 010 1.414z"
                                    clip-rule="evenodd"
                                ></path>
                            </svg>
                            {{ $t('Previous') }}
                        </a>
                        <a
                            v-if="fetchOptions.page < Math.ceil(paymentHistoriesTotal / fetchOptions.itemsPerPage)"
                            @click="nextPage"
                            href="#"
                            class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white"
                        >
                            {{ $t('Next') }}
                            <svg
                                aria-hidden="true"
                                class="w-5 h-5 ml-2"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    fill-rule="evenodd"
                                    d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z"
                                    clip-rule="evenodd"
                                ></path>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { storeToRefs } from 'pinia'
import { useAuthStore } from '~~/stores/auth'
import { usePaymentsStore, TOKEN_PROD_ID } from '~~/stores/payments'
import PaymentHistoryAddTokens from '~/components/PaymentHistoryAddTokens.vue'
import PaymentHistoryChangePlan from '~/components/PaymentHistoryChangePlan.vue'
import PaymentHistoryExtendFeature from '~/components/PaymentHistoryExtendFeature.vue'
import Loading from 'vue-loading-overlay'
const { $abbreviatedUnit } = useNuxtApp()

const authStore = useAuthStore()
const { isLoggingIn, user, isLoggedIn } = storeToRefs(authStore)

const paymentsStore = usePaymentsStore()
const { paymentHistories, paymentHistoriesTotal, fetchOptions, loading } = storeToRefs(paymentsStore)

const formatUserTokens = (number: number) => {
    return new Intl.NumberFormat().format(number.toFixed(0))
}

const paymentHistoryItemComponents = {
    '1': PaymentHistoryChangePlan,
    '2': PaymentHistoryAddTokens,
    '3': PaymentHistoryExtendFeature,
}

onMounted(() => {
    fetchOptions.value.page = 1
    paymentsStore.getOrderHistories()
})

const previousPage = async () => {
    fetchOptions.value.page = fetchOptions.value.page > 1 ? fetchOptions.value.page - 1 : 1
    await paymentsStore.getOrderHistories()
}

const nextPage = async () => {
    fetchOptions.value.page++
    await paymentsStore.getOrderHistories()
}
</script>
