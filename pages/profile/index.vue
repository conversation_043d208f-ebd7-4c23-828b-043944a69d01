<template>
  <div v-if="user" class="space-y-3 p-0 md:p-4">
    <div class="p-6">
      <div class="">
        <h4 class="font-semibold text-lg">
          {{ $t("Change account name") }}
        </h4>
        <hr class="h-px mt-2 mb-5 bg-gray-200 border-0 dark:bg-gray-700" />
        <form @submit.prevent="updateUserFullName" class="md:max-w-md">
          <div class="relative">
            <input
              type="text"
              id="first_name"
              class="block w-full p-4 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-500 placeholder-gray-300 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
              v-model="user.full_name"
              maxlength="50"
              :class="{
                'bg-red-50 border border-red-500 text-red-900 placeholder-red-700 focus:ring-red-500 dark:bg-gray-700 focus:border-red-500 dark:text-red-500 dark:placeholder-red-500 dark:border-red-500': updateInfoError,
              }"
            />
            <button
              type="submit"
              :disabled="isUpdatingUserInfo || !user.full_name"
              class="flex flex-inline text-white absolute right-2.5 bottom-2.5 font-medium rounded-lg text-sm px-4 py-2"
              :class="{
                'bg-gray-300 dark:bg-gray-600 cursor-not-allowed': !user.full_name,
                'bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800':
                  user.full_name,
              }"
            >
              <span>{{ $t("Update") }}</span>

              <svg
                v-if="isUpdatingUserInfo"
                aria-hidden="true"
                class="inline w-5 h-5 ml-2 text-white animate-spin dark:text-white fill-primary-600"
                viewBox="0 0 100 101"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                  fill="currentColor"
                />
                <path
                  d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                  fill="currentFill"
                />
              </svg>
              <svg
                v-else-if="updatedUserFullNameSuccess"
                class="w-5 h-5 ml-2 text-white animate-pulse"
                aria-hidden="true"
                xmlns="http://www.w3.org/2000/svg"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z"
                />
              </svg>
            </button>
          </div>
          <p v-if="updateInfoError" class="mt-2 text-sm text-red-600 dark:text-red-500">
            {{ i18n.t(updateInfoError?.detail.error_message || "") }}
          </p>
        </form>
      </div>
    </div>

    <div class="p-6">
      <div class="">
        <h4 class="font-semibold text-lg">
          {{ $t("Change password") }}
        </h4>
        <hr class="h-px mt-2 mb-5 bg-gray-200 border-0 dark:bg-gray-700" />
        <form @submit.prevent="changeUserPassword" class="md:max-w-md">
          <div class="mb-6">
            <label
              for="password"
              class="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
            >
              {{ $t("Current password") }}
            </label>
            <input
              v-model="changePasswordFormState.password"
              type="password"
              id="password"
              class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-500 placeholder-gray-300 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
              placeholder="•••••••••"
              required
              :class="{
                'bg-red-50 border border-red-500 text-red-900 placeholder-red-700 focus:ring-red-500 dark:bg-gray-700 focus:border-red-500 dark:text-red-500 dark:placeholder-red-500 dark:border-red-500':
                  v$.password.$error,
              }"
            />
            <p
              v-if="v$.password.$errors"
              class="mt-2 text-sm text-red-600 dark:text-red-500"
            >
              {{ v$.password.$errors[0]?.$message || "" }}
            </p>
          </div>
          <div class="mb-6">
            <label
              for="new_password"
              class="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
            >
              {{ $t("New password") }}
            </label>
            <input
              v-model="changePasswordFormState.new_password"
              type="password"
              id="new_password"
              class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-500 placeholder-gray-300 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
              placeholder="•••••••••"
              required
              :class="{
                'bg-red-50 border border-red-500 text-red-900 placeholder-red-700 focus:ring-red-500 dark:bg-gray-700 focus:border-red-500 dark:text-red-500 dark:placeholder-red-500 dark:border-red-500':
                  v$.new_password.$error,
              }"
            />
            <p
              v-if="v$.new_password.$errors"
              class="mt-2 text-sm text-red-600 dark:text-red-500"
            >
              {{ v$.new_password.$errors[0]?.$message }}
            </p>
          </div>
          <div class="mb-10">
            <label
              for="confirm_password"
              class="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
            >
              {{ $t("Confirm new password") }}
            </label>
            <input
              v-model="changePasswordFormState.new_password_confirm"
              type="password"
              id="confirm_password"
              class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-500 placeholder-gray-300 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
              placeholder="•••••••••"
              required
              :class="{
                'bg-red-50 border border-red-500 text-red-900 placeholder-red-700 focus:ring-red-500 dark:bg-gray-700 focus:border-red-500 dark:text-red-500 dark:placeholder-red-500 dark:border-red-500':
                  v$.new_password_confirm.$error,
              }"
            />
            <p
              v-if="v$.new_password_confirm.$errors"
              class="text-sm text-red-600 dark:text-red-500"
            >
              {{ v$.new_password_confirm.$errors[0]?.$message }}
            </p>
          </div>
          <p
            v-if="changePasswordError"
            class="mb-4 text-sm text-red-600 dark:text-red-500"
          >
            {{ i18n.t(changePasswordError?.detail.error_code || "") }}
          </p>
          <button
            type="submit"
            :disabled="isChangingPassword"
            class="text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
          >
            <span>{{ $t("Change password") }}</span>

            <svg
              v-if="isChangingPassword"
              aria-hidden="true"
              class="inline ml-2 -mr-1 w-5 h-5 text-white animate-spin dark:text-white fill-primary-600"
              viewBox="0 0 100 101"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                fill="currentColor"
              />
              <path
                d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                fill="currentFill"
              />
            </svg>
            <svg
              v-else-if="updatedUserPasswordSuccess"
              class="w-5 h-5 ml-2 -mr-1 text-white animate-pulse"
              aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z"
              />
            </svg>
            <svg
              v-else
              aria-hidden="true"
              class="w-5 h-5 ml-2 -mr-1"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fill-rule="evenodd"
                d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
                clip-rule="evenodd"
              ></path>
            </svg>
          </button>
        </form>
      </div>
    </div>

    <div>
      <div class="px-6">
        <div class="">
          <h4 class="font-semibold text-lg">
            {{ $t("Danger Zone") }}
          </h4>
          <hr class="h-px mt-2 mb-5 bg-gray-200 border-0 dark:bg-gray-700" />
          <button
            @click="deleteAccount"
            type="button"
            class="text-white bg-gradient-to-r from-red-400 via-red-500 to-red-600 hover:bg-gradient-to-br focus:ring-4 focus:outline-none focus:ring-red-300 dark:focus:ring-red-800 font-medium rounded-lg text-sm px-5 py-2.5 text-center me-2 mb-2"
          >
            {{ $t("Delete account") }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, reactive } from "vue";
import { storeToRefs } from "pinia";
import { useAuthStore } from "~~/stores/auth";
import { useUserStore } from "~/stores/user";
import { createI18nMessage } from "@vuelidate/validators";
import { useVuelidate } from "@vuelidate/core";
import * as validators from "@vuelidate/validators";
import { useI18n } from "vue-i18n";
import DeleteAccountModal from '~/base-components/DeleteAccountModal.vue'
import { ModalsContainer, useModal } from 'vue-final-modal'

const { open, close, options } = useModal({
    component: DeleteAccountModal,
    attrs: {
        onClose() {
            close()
        },
        onSubmit() {
            close()
        },
    },
})

const i18n = useI18n();
const withI18nMessage = createI18nMessage({ t: i18n.t.bind(i18n) });
const authStore = useAuthStore();
const userStore = useUserStore();
const { user } = storeToRefs(authStore);
const {
  updateInfoError,
  changePasswordError,
  isChangingPassword,
  isUpdatingUserInfo,
  isOpenDeleteAccountModal,
  confirmDeleteAccount
} = storeToRefs(userStore);
// const { resetPasswordError, resetPasswordEmail } = storeToRefs(authStore)
const required = withI18nMessage(validators.required);
const email = withI18nMessage(validators.email);
const minLength = (v) => withI18nMessage(validators.minLength(v));
const sameAs = (v) => withI18nMessage(validators.sameAs(v));
const deleteAccount = async () => {
  isOpenDeleteAccountModal.value = true;
  open()
};

const changePasswordFormState = reactive({
  password: "",
  new_password: "",
  new_password_confirm: "",
});

const formRules = computed(() => ({
  password: { required, minLength: minLength(6) },
  new_password: { required, minLength: minLength(6) },
  new_password_confirm: {
    required,
    minLength: minLength(6),
    sameAsPassword: sameAs(changePasswordFormState.new_password),
  },
}));
const v$ = useVuelidate(formRules, changePasswordFormState);

const updatedUserFullNameSuccess = ref(false);
const updateUserFullName = async () => {
  updateInfoError.value = null;
  const success = await userStore.updateInfo({ full_name: user.value?.full_name });
  if (success) {
    updatedUserFullNameSuccess.value = true;
    setTimeout(() => {
      updatedUserFullNameSuccess.value = false;
    }, 1700);
  }
};
const updatedUserPasswordSuccess = ref(false);
const changeUserPassword = async () => {
  const result = await v$.value.$validate();
  if (!result) {
    console.log("Validation failed");
    console.log(v$.value.$errors[0]);
    return;
  }

  changePasswordError.value = null;
  const success = await userStore.changePassword(changePasswordFormState);
  if (success) {
    console.log("updated password");
    updatedUserPasswordSuccess.value = true;
    setTimeout(() => {
      updatedUserPasswordSuccess.value = false;
    }, 1700);
  }
};
</script>
