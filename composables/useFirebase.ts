import { initializeApp } from 'firebase/app'
import { initializeAppCheck, ReCaptchaV3Provider } from 'firebase/app-check'
import { getAuth } from 'firebase/auth'

// import { getDatabase, ref, onValue } from "firebase/database";

export const useFirebase = () => {
    const config = useRuntimeConfig()
    const firebaseConfig = {
        apiKey: config.public.NUXT_FIREBASE_API_KEY,
        authDomain: config.public.NUXT_FIREBASE_AUTH_DOMAIN,
        databaseURL: config.public.NUXT_FIREBASE_DATABASE_URL,
        projectId: config.public.NUXT_FIREBASE_PROJECT_ID,
        storageBucket: config.public.NUXT_FIREBASE_STORAGE_BUCKET,
        messagingSenderId: config.public.NUXT_FIREBASE_MESSAGING_SENDER_ID,
        appId: config.public.NUXT_FIREBASE_APP_ID,
    }
    // Initialize Firebase
    const firebaseApp = initializeApp(firebaseConfig)
    const auth = getAuth(firebaseApp)
    let appCheck = null
    if (config.public.NUXT_RECAPCHA_V3_SITE_KEY) {
        appCheck = initializeAppCheck(firebaseApp, {
            provider: new ReCaptchaV3Provider(config.public.NUXT_RECAPCHA_V3_SITE_KEY),

            // Optional argument. If true, the SDK automatically refreshes App Check
            // tokens as needed.
            isTokenAutoRefreshEnabled: true,
        })
    }

    return {
        firebaseApp,
        appCheck,
        auth
    }
}
