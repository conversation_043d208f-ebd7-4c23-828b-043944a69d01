// @ts-ignore
import { SSE } from 'sse.js'
import axios from 'axios'
import { useAuthStore } from '~/stores/auth'
import { useTranslateStore } from '~/stores/translate'

let isRefreshingToken = false
let requestsPending = []

interface RefreshTokenResponse {
    access_token: string
    refresh_token?: string
}

export const useAPI: typeof useFetch = (request, opts?, token = null) => {
    const config = useRuntimeConfig()
    const access_token = localStorage.getItem('access_token')
    const refresh_token = localStorage.getItem('refresh_token')
    const headers = {
        Authorization: '',
    }
    if (access_token) {
        headers.Authorization = `Bearer ${token ? token : access_token}`
    }

    return useFetch(request, {
        baseURL: config.public.baseURL,
        ...opts,
        headers,
        async onResponseError({ request, response, options }) {
            let _errorResponse = response
            const originalRequest = options
            const status = _errorResponse.status
            const authStore = useAuthStore()
            switch (status) {
                case 403:
                    if (response.url.includes('/refresh-token') || !refresh_token) {
                        authStore.setIsExpired(true)
                        authStore.logout()
                        return Promise.reject(response)
                    } else {
                        //retry with refrest token first
                        if (!originalRequest.retry) {
                            // save request if it is not refresh token request
                            if (isRefreshingToken) {
                                return new Promise(function (resolve, reject) {
                                    requestsPending.push(function () {
                                        resolve(useAPI(request, options))
                                    })
                                })
                            }
                            originalRequest.retry = 1
                            isRefreshingToken = true

                            try {
                                const { data }: { data: Ref<RefreshTokenResponse>; error: any } = await useAPI(
                                    '/refresh-token',
                                    {
                                        method: 'POST',
                                        body: {
                                            refresh_token,
                                        },
                                    }
                                )

                                if (data.value) {
                                    localStorage.setItem('access_token', data.value.access_token)
                                    localStorage.setItem('refresh_token', data.value.refresh_token || '')
                                    authStore.fetchUserInfo()
                                    return useAPI(request, options)
                                } else {
                                    authStore.setIsExpired(true)
                                    authStore.logout()
                                }
                            } catch (refreshError) {
                                isRefreshingToken = false
                                authStore.setIsExpired(true)
                                authStore.logout()
                            } finally {
                                isRefreshingToken = false
                                requestsPending.forEach((callback) => callback())
                                requestsPending = []
                            }
                        } else {
                            authStore.setIsExpired(true)
                            authStore.logout()
                        }
                    }
                    break
                default:
                    break
            }

            // return Promise.reject(response._data)
        },
    })
}

export const useAxios = (request: any, opts?: any) => {
    const config = useRuntimeConfig()
    const access_token = localStorage.getItem('access_token')
    const refresh_token = localStorage.getItem('refresh_token')
    const headers = {
        Authorization: '',
    }
    if (access_token) {
        headers.Authorization = `Bearer ${access_token}`
    }

    const axiosInstance = axios.create({
        baseURL: config.public.baseURL,
        headers,
        ...opts,
    })

    axiosInstance.interceptors.response.use(
        (config) => {
            return config
        },
        async (error) => {
            console.log('🚀 ~ file: useAPI.ts:117 ~ error:', error)
            let _errorResponse = error.response
            const originalRequest = error.config
            const status = _errorResponse.status
            const authStore = useAuthStore()
            switch (status) {
                case 403:
                    if (['/refresh-token'].includes(originalRequest.url) || !refresh_token) {
                        authStore.setIsExpired(true)
                        authStore.logout()
                        return Promise.reject(error)
                    } else {
                        //retry with refrest token first
                        if (!originalRequest._retry) {
                            // save request if it is not refresh token request
                            if (isRefreshingToken) {
                                return new Promise(function (resolve, reject) {
                                    requestsPending.push(function () {
                                        resolve(axiosInstance(originalRequest))
                                    })
                                })
                            }
                            originalRequest._retry = true
                            isRefreshingToken = true

                            try {
                                var refreshTokenRes = await axiosInstance.post('/refresh-token', {
                                    refresh_token,
                                })

                                if (refreshTokenRes.status === 200) {
                                    localStorage.setItem('access_token', refreshTokenRes.data.access_token)
                                    localStorage.setItem('refresh_token', refreshTokenRes.data.refresh_token)
                                    authStore.fetchUserInfo()
                                    originalRequest.headers['Authorization'] =
                                        'Bearer ' + refreshTokenRes.data.access_token
                                    return axiosInstance(originalRequest)
                                } else {
                                    authStore.setIsExpired(true)
                                    authStore.logout()
                                }
                            } catch (refreshError) {
                                isRefreshingToken = false
                                authStore.setIsExpired(true)
                                authStore.logout()
                            } finally {
                                isRefreshingToken = false
                                requestsPending.forEach((callback) => callback())
                                requestsPending = []
                            }
                        } else {
                            authStore.setIsExpired(true)
                            authStore.logout()
                        }
                    }
                    break
                default:
                    break
            }
            return Promise.reject(error)
        }
    )

    return axiosInstance(request)
}
export const useStreamAPI: typeof SSE = (url: string, payload: object) => {
    const config = useRuntimeConfig()
    const access_token = localStorage.getItem('access_token')
    const refresh_token = localStorage.getItem('refresh_token')
    const headers = {
        Authorization: '',
    }
    if (access_token) {
        headers.Authorization = `Bearer ${access_token}`
    }

    const sse = new SSE(config.public.baseURL + url, {
        headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${access_token}`,
        },
        method: 'POST',
        payload: JSON.stringify(payload),
    })
    sse._onStreamFailure = async (err: any) => {
        const authStore = useAuthStore()
        const translateStore = useTranslateStore()
        translateStore.setIsTranslating(false)
        if (err?.srcElement?.status === 403) {
            console.log('403')
            try {
                if (!isRefreshingToken && requestsPending.length === 0) {
                    isRefreshingToken = true
                    requestsPending.push(translateStore.translate)
                }
            } catch (refreshError) {
                isRefreshingToken = false
                authStore.setIsExpired(true)
                authStore.logout()
            } finally {
                isRefreshingToken = false
            }
        }
    }

    return sse
}
