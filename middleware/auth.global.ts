export const PROTECTED_ROUTES = [
    "profile",
    "profile-add-tokens",
    "profile-change-plan",
    "profile-thank-you",
    "profile-payment-history",
    "profile-cancel-successful",
]

export default defineNuxtRouteMiddleware(async (to, from) => {
    if (PROTECTED_ROUTES.includes(to.name as string)) {
        if (localStorage.getItem('access_token')) {
            return
        } else {
            return navigateTo('/signin')
        }
    }
})