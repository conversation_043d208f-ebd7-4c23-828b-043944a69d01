import { createI18n } from 'vue-i18n'
import en from '../locales/en.json'
import zh from '../locales/zh.json'
import ja from '../locales/ja.json'
import vi from '../locales/vi.json'

import dayjs from 'dayjs'
import 'dayjs/locale/en'
import 'dayjs/locale/zh'
import 'dayjs/locale/ja'
import 'dayjs/locale/vi'

export default defineNuxtPlugin(({ vueApp }) => {
    const i18n = createI18n({
        legacy: false,
        globalInjection: true,
        locale: window.localStorage.getItem('locale') || 'en',
        silentFallbackWarn: true,
        messages: {
            en,
            zh,
            ja,
            vi,
        },
    })

    dayjs.locale(window.localStorage.getItem('locale') || 'en')

    vueApp.use(i18n)
})
