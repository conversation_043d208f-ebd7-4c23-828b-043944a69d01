export default defineNuxtPlugin(() => {
    return {
        provide: {
            fileSizeFormat: (fileSizeInBytes: number) => {
                if (fileSizeInBytes < 1000) {
                    return fileSizeInBytes + ' B';
                } else if (fileSizeInBytes < 1000 * 1000) {
                    return (fileSizeInBytes / 1000).toFixed(2) + ' KB';
                } else if (fileSizeInBytes < 1000 * 1000 * 1000) {
                    return (fileSizeInBytes / (1000 * 1000)).toFixed(2) + ' MB';
                } else {
                    return (fileSizeInBytes / (1000 * 1000 * 1000)).toFixed(2) + ' GB';
                }
            }
        }
    }
})