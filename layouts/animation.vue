<script setup>
import TheTermsOfService from '~/components/TheTermsOfService.vue'
import Languages from '~/components/Languages.vue'
import DarkModeToggle from '~/components/DarkModeToggle.vue'
import { onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
const { locale } = useI18n()
useHead({
    htmlAttrs: {
        lang: locale,
    },
})
onMounted(() => {
    if (process.client) {
        // window?.tsParticles?.load('tsparticles', {
        //     fullScreen: false,
        //     // background: { image: 'linear-gradient(90deg, #DE26A0 0%, #068FF1 100%)' },
        //     particles: {
        //         number: { value: 80, density: { enable: true, value_area: 800 } },
        //         color: { value: '#ffffff' },
        //         shape: {
        //             type: 'circle',
        //             stroke: { width: 0, color: '#000000' },
        //             polygon: { nb_sides: 5 },
        //             image: { src: 'img/github.svg', width: 100, height: 100 },
        //         },
        //         opacity: {
        //             value: 0.5,
        //             random: false,
        //             anim: { enable: false, speed: 1, opacity_min: 0.1, sync: false },
        //         },
        //         size: { value: 3, random: true, anim: { enable: false, speed: 40, size_min: 0.1, sync: false } },
        //         line_linked: { enable: true, distance: 150, color: '#ffffff', opacity: 0.4, width: 1 },
        //         move: {
        //             enable: true,
        //             speed: 3,
        //             direction: 'none',
        //             random: false,
        //             straight: false,
        //             out_mode: 'out',
        //             bounce: false,
        //             attract: { enable: false, rotateX: 600, rotateY: 1200 },
        //         },
        //     },
        //     interactivity: {
        //         detect_on: 'canvas',
        //         events: {
        //             onhover: { enable: true, mode: 'repulse' },
        //             onclick: { enable: true, mode: 'push' },
        //             resize: true,
        //         },
        //         modes: {
        //             grab: { distance: 400, line_linked: { opacity: 1 } },
        //             bubble: { distance: 400, size: 40, duration: 2, opacity: 8, speed: 3 },
        //             repulse: { distance: 200, duration: 0.4 },
        //             push: { particles_nb: 4 },
        //             remove: { particles_nb: 2 },
        //         },
        //     },
        //     retina_detect: true,
        // })
    }
})
</script>

<template>
    <!-- <div id="tsparticles" class="w-full h-full absolute z-20"></div> -->

    <section
        class="bg-cover bg-no-repeat bg-[url('~/assets/images/bg.jpg')] bg-primary-400 bg-blend-darken dark:bg-blend-multiply dark:bg-primary-800 min-h-[760px]"
    >
        <ul class="circles">
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
        </ul>

        <div class="z-10 min-h-[600px]"><slot /></div>

        <div class="absolute bottom-5 w-screen text-center text-white text-sm">© Copyright 2023, DoctransGPT</div>
        <div class="absolute flex flex-inline top-5 right-10 space-x-3 items-center z-30">
            <div>
                <Languages class="text-white" />
            </div>
            <div>
                <DarkModeToggle dark />
            </div>
        </div>
    </section>
    <TheTermsOfService />
</template>

<style scoped>
.circles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.circles li {
    position: absolute;
    display: block;
    list-style: none;
    width: 20px;
    height: 20px;
    background: rgba(255, 255, 255, 0.2);
    animation: animate 25s linear infinite;
    bottom: -150px;
}

.circles li:nth-child(1) {
    left: 25%;
    width: 80px;
    height: 80px;
    animation-delay: 0s;
}

.circles li:nth-child(2) {
    left: 10%;
    width: 20px;
    height: 20px;
    animation-delay: 2s;
    animation-duration: 12s;
}

.circles li:nth-child(3) {
    left: 70%;
    width: 20px;
    height: 20px;
    animation-delay: 4s;
}

.circles li:nth-child(4) {
    left: 40%;
    width: 60px;
    height: 60px;
    animation-delay: 0s;
    animation-duration: 18s;
}

.circles li:nth-child(5) {
    left: 65%;
    width: 20px;
    height: 20px;
    animation-delay: 0s;
}

.circles li:nth-child(6) {
    left: 75%;
    width: 110px;
    height: 110px;
    animation-delay: 3s;
}

.circles li:nth-child(7) {
    left: 35%;
    width: 150px;
    height: 150px;
    animation-delay: 7s;
}

.circles li:nth-child(8) {
    left: 50%;
    width: 25px;
    height: 25px;
    animation-delay: 15s;
    animation-duration: 45s;
}

.circles li:nth-child(9) {
    left: 20%;
    width: 15px;
    height: 15px;
    animation-delay: 2s;
    animation-duration: 35s;
}

.circles li:nth-child(10) {
    left: 85%;
    width: 150px;
    height: 150px;
    animation-delay: 0s;
    animation-duration: 11s;
}

@keyframes animate {
    0% {
        transform: translateY(0) rotate(0deg);
        opacity: 1;
        border-radius: 0;
    }

    100% {
        transform: translateY(-1000px) rotate(720deg);
        opacity: 0;
        border-radius: 50%;
    }
}
</style>
