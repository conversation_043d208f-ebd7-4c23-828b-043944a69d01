<script lang="ts" setup>
import TheHeader from '~/components/DefaultLayout/TheHeader.vue'
import TheFooter from '~/components/DefaultLayout/TheFooter.vue'
import TheDrawer from '~/components/DefaultLayout/TheDrawer.vue'
import TheLanguageBar from '~/components/DefaultLayout/TheLanguageBar.vue'
import MobileBottomMenu from '~/components/MobileBottomMenu.vue'
import { ModalsContainer } from 'vue-final-modal'


const route = useRoute()
const config = useRuntimeConfig()
const isMaintenance = config.public.NUXT_MAINTENANCE_TRANSLATE_DOCUMENT === 'true'
import { useI18n } from 'vue-i18n'
const { locale } = useI18n()
useHead({
    htmlAttrs: {
        lang: locale,
    },
})
</script>

<template>
    <div class="flex flex-col h-screen w-screen">
        <div class="shrink-0">
            <TheHeader />
        </div>
        <div class="flex-1 w-full md:max-w-screen-xl mx-auto">
            <div class="flex flex-col h-full w-full">
                <div v-if="!isMaintenance || route.name !== 'documents'" class="shrink-0 sticky top-0 z-20">
                    <TheLanguageBar />
                </div>
                <div class="flex-1 pb-44 md:pb-0">
                    <slot />
                    <ModalsContainer />
                </div>
            </div>
        </div>

        <div class="shrink-0">
            <TheFooter />
        </div>
    </div>
    <TheDrawer />
    <MobileBottomMenu />
    <TheNotificationPopup />
</template>

<style scoped></style>
