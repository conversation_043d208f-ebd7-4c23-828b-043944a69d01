<script lang="ts" setup>
import TheHeader from '~/components/DefaultLayout/TheHeader.vue'
import TheFooter from '~/components/DefaultLayout/TheFooter.vue'
import TheDrawer from '~/components/DefaultLayout/TheDrawer.vue'
import { ModalsContainer } from 'vue-final-modal'
import { useI18n } from 'vue-i18n'
const { locale } = useI18n()
useHead({
    htmlAttrs: {
        lang: locale,
    },
})
</script>

<template>
    <div class="flex flex-col w-screen">
        <div class="shrink-0">
            <TheHeader />
        </div>
        <div class="w-full md:max-w-screen-xl mx-auto">
            <slot />
            <ModalsContainer />
        </div>
        <div class="shrink-0">
            <TheFooter />
        </div>
        <TheDrawer />
        <TheNotificationPopup />
    </div>
</template>
